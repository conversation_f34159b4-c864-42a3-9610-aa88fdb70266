const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testAPI() {
    try {
        console.log('Testing Torbaaz API endpoints...\n');
        
        // Test restaurants endpoint
        console.log('1. Testing /api/restaurants');
        const restaurantsResponse = await fetch('http://localhost:3000/api/restaurants');
        const restaurants = await restaurantsResponse.json();
        console.log(`✅ Found ${restaurants.length} restaurants`);
        console.log(`First restaurant: ${restaurants[0]?.name || 'None'}\n`);
        
        // Test specific restaurant
        if (restaurants.length > 0) {
            // Use the first restaurant ID from the API response
            const restaurantId = restaurants[0].id;
            console.log(`2. Testing /api/restaurants/${restaurantId}`);
            const restaurantResponse = await fetch(`http://localhost:3000/api/restaurants/${restaurantId}`);
            const restaurant = await restaurantResponse.json();
            console.log(`✅ Restaurant details: ${restaurant.name}\n`);

            // Test menu items
            console.log(`3. Testing /api/restaurants/${restaurantId}/menu`);
            const menuResponse = await fetch(`http://localhost:3000/api/restaurants/${restaurantId}/menu`);
            const menu = await menuResponse.json();
            console.log(`✅ Found ${menu?.length || 0} menu items\n`);
        }
        
        // Test deals
        console.log('4. Testing /api/restaurants/deals');
        const dealsResponse = await fetch('http://localhost:3000/api/restaurants/deals');
        const deals = await dealsResponse.json();
        console.log(`✅ Found ${deals?.length || 0} deals\n`);
        
        // Test chat
        console.log('5. Testing /api/chat/chat');
        const chatResponse = await fetch('http://localhost:3000/api/chat/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                userId: 'test_user',
                text: 'Hello, what restaurants do you have?'
            })
        });
        const chatResult = await chatResponse.json();
        console.log(`✅ Chat response: ${chatResult.reply?.substring(0, 100)}...\n`);
        
        console.log('🎉 All API tests completed successfully!');
        
    } catch (error) {
        console.error('❌ API test failed:', error.message);
    }
}

testAPI();
