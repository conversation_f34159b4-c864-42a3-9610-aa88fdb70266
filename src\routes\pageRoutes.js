const express = require('express');
const router = express.Router();
const path = require('path');

// Home page route
router.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../../public/index.html'));
});

// Restaurants page route
router.get('/restaurants', (req, res) => {
    res.sendFile(path.join(__dirname, '../../public/restaurants.html'));
});

// Deals page route
router.get('/deals', (req, res) => {
    res.sendFile(path.join(__dirname, '../../public/deals.html'));
});

// Orders page route
router.get('/orders', (req, res) => {
    res.sendFile(path.join(__dirname, '../../public/orders.html'));
});

// About page route
router.get('/about', (req, res) => {
    res.sendFile(path.join(__dirname, '../../public/about.html'));
});

// Cart page route
router.get('/cart', (req, res) => {
    res.sendFile(path.join(__dirname, '../../public/cart.html'));
});

// Catch-all route for handling 404s - must be last
router.get('*', (req, res) => {
    res.status(404).sendFile(path.join(__dirname, '../../public/index.html'));
});

module.exports = router;