* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

:root {
    --primary-color: #2196F3;
    --primary-dark: #1976D2;
    --error-color: #f44336;
    --success-color: #4CAF50;
    --text-primary: #2c3e50;
    --text-secondary: #666;
    --background-light: #f5f5f5;
    --border-color: #e0e0e0;
    --card-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

body {
    background-color: var(--background-light);
    color: var(--text-primary);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    background-color: #fff;
    padding: 20px;
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    margin-bottom: 20px;
}

h1 {
    color: var(--text-primary);
    margin-bottom: 20px;
    font-size: 2.5em;
    text-align: center;
}

.search-container {
    display: flex;
    gap: 10px;
    max-width: 600px;
    margin: 0 auto;
}

input[type="text"] {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s;
}

input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

button {
    padding: 12px 25px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 8px;
}

button:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

button i {
    font-size: 14px;
}

main {
    display: grid;
    grid-template-columns: 1.5fr 1fr;
    gap: 20px;
}

.restaurants-container {
    background-color: #fff;
    padding: 20px;
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    min-height: 400px;
}

/* Loading State */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: var(--text-secondary);
    font-size: 1.1em;
}

.loading::before {
    content: '';
    width: 24px;
    height: 24px;
    margin-right: 12px;
    border: 3px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
    text-align: center;
    padding: 40px 20px;
    color: var(--error-color);
}

.error-message i {
    font-size: 48px;
    margin-bottom: 16px;
}

.error-message p {
    margin-bottom: 20px;
    color: var(--text-secondary);
}

.error-message button {
    margin: 0 auto;
    background-color: var(--error-color);
}

.error-message button:hover {
    background-color: #d32f2f;
}

/* No Results */
.no-results {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.no-results i {
    font-size: 48px;
    margin-bottom: 16px;
    color: var(--text-secondary);
}

.restaurant-card {
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    gap: 20px;
    align-items: center;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.restaurant-image {
    width: 120px;
    height: 120px;
    border-radius: 8px;
    object-fit: cover;
}

.restaurant-info {
    flex: 1;
}

.restaurant-card h3 {
    color: var(--text-primary);
    margin-bottom: 10px;
    font-size: 1.4em;
}

.restaurant-card p {
    color: var(--text-secondary);
    margin-bottom: 8px;
    font-size: 0.95em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.rating {
    color: #f39c12;
    font-weight: 500;
}

/* Restaurant Details in Chat */
.restaurant-details {
    background-color: #fff;
    border-radius: 12px;
    padding: 16px;
    margin-top: 8px;
}

.restaurant-details h3 {
    color: var(--text-primary);
    margin-bottom: 12px;
    font-size: 1.3em;
}

.restaurant-details h4 {
    color: var(--text-primary);
    margin: 16px 0 12px;
    font-size: 1.1em;
}

.menu-items {
    display: grid;
    gap: 12px;
    margin-top: 8px;
}

.menu-item {
    background-color: var(--background-light);
    padding: 12px;
    border-radius: 8px;
}

.menu-item h5 {
    color: var(--text-primary);
    margin-bottom: 4px;
    font-size: 1em;
}

.menu-item .price {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 4px;
}

.menu-item .description {
    color: var(--text-secondary);
    font-size: 0.9em;
}

.chat-container {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    display: flex;
    flex-direction: column;
    height: 700px;
}

.chat-header {
    padding: 20px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 15px 15px 0 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.chat-header h2 {
    font-size: 1.4em;
    font-weight: 500;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.message {
    padding: 12px 16px;
    border-radius: 12px;
    max-width: 85%;
    line-height: 1.5;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.user-message {
    background-color: #E3F2FD;
    margin-left: auto;
    border-bottom-right-radius: 4px;
}

.assistant-message {
    background-color: var(--background-light);
    margin-right: auto;
    border-bottom-left-radius: 4px;
}

.chat-input {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 10px;
    background-color: #fff;
    border-radius: 0 0 15px 15px;
}

.chat-input input {
    flex: 1;
}

@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
    }
    
    .chat-container {
        height: 500px;
    }

    .restaurant-card {
        flex-direction: column;
        text-align: center;
    }

    .restaurant-image {
        width: 100%;
        height: 200px;
    }

    .restaurant-card p {
        justify-content: center;
    }

    h1 {
        font-size: 2em;
    }

    .search-container {
        flex-direction: column;
    }

    button {
        width: 100%;
        justify-content: center;
    }

    .filters {
        padding: 10px;
        margin: 0 -20px;
        padding-left: 20px;
    }

    .filter-btn {
        flex-shrink: 0;
    }

    .view-menu-btn {
        margin-top: 15px;
    }
}

/* Restaurant Section */
.restaurants-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Filters */
.filters {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    padding: 10px 0;
    -webkit-overflow-scrolling: touch;
}

.filter-btn {
    white-space: nowrap;
    padding: 8px 16px;
    border-radius: 20px;
    background-color: var(--background-light);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* View Menu Button */
.view-menu-btn {
    margin-top: 10px;
    width: 100%;
    padding: 8px 16px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.view-menu-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

/* Footer */
footer {
    text-align: center;
    padding: 20px;
    color: var(--text-secondary);
    border-top: 1px solid var(--border-color);
    margin-top: 40px;
}

/* Loading Animation */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.loading i {
    animation: pulse 1.5s infinite;
}

/* Scroll Animations */
.restaurant-card {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s, transform 0.5s;
}

.restaurant-card.show {
    opacity: 1;
    transform: translateY(0);
}

.restaurant-card {
    transition: transform 0.3s, box-shadow 0.3s;
}

.restaurant-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

/* Content Layout */
.content-section {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Deals Section */
.deals-section {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    padding: 20px;
}

.deals-section h2 {
    color: var(--text-primary);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.deals-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.deal-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 15px;
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
}

.deal-card::before {
    content: 'SPECIAL OFFER';
    position: absolute;
    top: 10px;
    right: -30px;
    background: var(--primary-color);
    color: white;
    padding: 5px 40px;
    transform: rotate(45deg);
    font-size: 0.8em;
    font-weight: 500;
}

.deal-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.deal-card h3 {
    color: var(--text-primary);
    margin-bottom: 10px;
    font-size: 1.2em;
}

.deal-price {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 15px 0;
}

.original-price {
    color: var(--text-secondary);
    text-decoration: line-through;
    font-size: 0.9em;
}

.discounted-price {
    color: var(--primary-color);
    font-size: 1.2em;
    font-weight: 600;
}

.deal-card .valid-until {
    color: var(--text-secondary);
    font-size: 0.9em;
    margin-top: 10px;
}

/* Restaurant Section Updates */
.restaurants-section {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    padding: 20px;
}

.restaurants-section h2 {
    color: var(--text-primary);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Footer Styles */
.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    padding: 40px 20px;
    background-color: #fff;
    border-radius: 15px 15px 0 0;
    box-shadow: var(--card-shadow);
}

.footer-section h3 {
    color: var(--text-primary);
    margin-bottom: 15px;
    font-size: 1.2em;
}

.footer-section p {
    color: var(--text-secondary);
    margin-bottom: 10px;
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding: 20px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 0 0 15px 15px;
}

/* Responsive Design Updates */
@media (max-width: 768px) {
    .content-section {
        gap: 20px;
    }

    .deals-container {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .footer-section ul {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 20px;
    }

    .footer-section ul li {
        margin: 0;
    }
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.deal-card {
    animation: slideIn 0.5s ease-out forwards;
}

/* Loading States */
.loading-deals {
    text-align: center;
    padding: 40px;
    color: var(--text-secondary);
}

.loading-deals i {
    font-size: 2em;
    margin-bottom: 10px;
    animation: spin 1s linear infinite;
}

/* Error States */
.error-state {
    text-align: center;
    padding: 40px;
    color: var(--error-color);
}

.error-state i {
    font-size: 2em;
    margin-bottom: 10px;
}

.error-state button {
    margin-top: 15px;
    background-color: var(--error-color);
} 