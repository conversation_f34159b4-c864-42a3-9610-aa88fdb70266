const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testDynamicContent() {
    console.log('🔍 Testing Dynamic Content Loading...\n');
    
    let passedTests = 0;
    let totalTests = 0;

    // Test 1: Restaurants API
    totalTests++;
    console.log('1. 🏪 Testing Restaurants API...');
    try {
        const response = await fetch('http://localhost:3000/api/restaurants');
        if (response.ok) {
            const restaurants = await response.json();
            if (Array.isArray(restaurants) && restaurants.length > 0) {
                console.log(`   ✅ Restaurants API: ${restaurants.length} restaurants loaded`);
                console.log(`   📝 Sample: ${restaurants[0].name}`);
                passedTests++;
            } else {
                console.log('   ❌ Restaurants API: No restaurants found');
            }
        } else {
            console.log('   ❌ Restaurants API: HTTP error');
        }
    } catch (error) {
        console.log('   ❌ Restaurants API: Connection error');
    }

    // Test 2: Deals API
    totalTests++;
    console.log('\n2. 🏷️ Testing Deals API...');
    try {
        const response = await fetch('http://localhost:3000/api/restaurants/deals');
        if (response.ok) {
            const deals = await response.json();
            if (Array.isArray(deals) && deals.length > 0) {
                console.log(`   ✅ Deals API: ${deals.length} deals loaded`);
                console.log(`   📝 Sample: ${deals[0].name} - Rs. ${deals[0].price}`);
                passedTests++;
            } else {
                console.log('   ❌ Deals API: No deals found');
            }
        } else {
            console.log('   ❌ Deals API: HTTP error');
        }
    } catch (error) {
        console.log('   ❌ Deals API: Connection error');
    }

    // Test 3: Restaurant Details API
    totalTests++;
    console.log('\n3. 📋 Testing Restaurant Details API...');
    try {
        // First get a restaurant ID
        const restaurantsResponse = await fetch('http://localhost:3000/api/restaurants');
        const restaurants = await restaurantsResponse.json();
        
        if (restaurants.length > 0) {
            const restaurantId = restaurants[0].id;
            const response = await fetch(`http://localhost:3000/api/restaurants/${restaurantId}/full`);
            
            if (response.ok) {
                const restaurant = await response.json();
                if (restaurant.menu && Array.isArray(restaurant.menu)) {
                    console.log(`   ✅ Restaurant Details: ${restaurant.name} with ${restaurant.menu.length} menu items`);
                    passedTests++;
                } else {
                    console.log('   ❌ Restaurant Details: Menu data missing');
                }
            } else {
                console.log('   ❌ Restaurant Details: HTTP error');
            }
        } else {
            console.log('   ❌ Restaurant Details: No restaurants to test');
        }
    } catch (error) {
        console.log('   ❌ Restaurant Details: Connection error');
    }

    // Test 4: Chat API
    totalTests++;
    console.log('\n4. 🤖 Testing Chat API...');
    try {
        const response = await fetch('http://localhost:3000/api/chat/chat', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                userId: 'test_dynamic_content',
                text: 'Show me restaurants'
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.reply && data.reply.length > 0) {
                console.log('   ✅ Chat API: AI assistant responding correctly');
                console.log(`   📝 Response: "${data.reply.substring(0, 100)}..."`);
                passedTests++;
            } else {
                console.log('   ❌ Chat API: Empty response');
            }
        } else {
            console.log('   ❌ Chat API: HTTP error');
        }
    } catch (error) {
        console.log('   ❌ Chat API: Connection error');
    }

    // Test 5: Search API
    totalTests++;
    console.log('\n5. 🔍 Testing Search API...');
    try {
        const response = await fetch('http://localhost:3000/api/restaurants/search?query=pizza');
        if (response.ok) {
            const results = await response.json();
            if (Array.isArray(results)) {
                console.log(`   ✅ Search API: Found ${results.length} results for "pizza"`);
                passedTests++;
            } else {
                console.log('   ❌ Search API: Invalid response format');
            }
        } else {
            console.log('   ❌ Search API: HTTP error');
        }
    } catch (error) {
        console.log('   ❌ Search API: Connection error');
    }

    // Test 6: Frontend JavaScript Files
    totalTests++;
    console.log('\n6. 📄 Testing Frontend JavaScript Files...');
    try {
        const fs = require('fs');
        const path = require('path');
        
        const jsFiles = [
            'public/js/main.js',
            'public/js/chat.js',
            'public/js/search.js',
            'public/js/deals.js',
            'public/js/cart.js'
        ];
        
        let allFilesExist = true;
        jsFiles.forEach(file => {
            if (!fs.existsSync(path.join(__dirname, file))) {
                console.log(`   ❌ Missing file: ${file}`);
                allFilesExist = false;
            }
        });
        
        if (allFilesExist) {
            console.log('   ✅ All JavaScript files present');
            passedTests++;
        }
    } catch (error) {
        console.log('   ❌ Error checking JavaScript files');
    }

    // Test 7: HTML Pages
    totalTests++;
    console.log('\n7. 🌐 Testing HTML Pages...');
    try {
        const fs = require('fs');
        const path = require('path');
        
        const htmlFiles = [
            'public/index.html',
            'public/about.html',
            'public/deals.html',
            'public/cart.html'
        ];
        
        let allPagesExist = true;
        htmlFiles.forEach(file => {
            if (!fs.existsSync(path.join(__dirname, file))) {
                console.log(`   ❌ Missing page: ${file}`);
                allPagesExist = false;
            }
        });
        
        if (allPagesExist) {
            console.log('   ✅ All HTML pages present');
            passedTests++;
        }
    } catch (error) {
        console.log('   ❌ Error checking HTML pages');
    }

    // Final Summary
    console.log('\n' + '='.repeat(60));
    console.log('🎯 DYNAMIC CONTENT TESTING SUMMARY');
    console.log('='.repeat(60));
    console.log(`✅ Tests Passed: ${passedTests}/${totalTests}`);
    console.log(`📊 Success Rate: ${Math.round((passedTests/totalTests) * 100)}%`);
    
    if (passedTests === totalTests) {
        console.log('\n🎉 ALL DYNAMIC CONTENT IS WORKING PERFECTLY!');
        console.log('🚀 Frontend Features Status:');
        console.log('   ✅ Restaurant listings loading dynamically');
        console.log('   ✅ Deals displaying with real data');
        console.log('   ✅ Restaurant details and menus working');
        console.log('   ✅ AI chat assistant fully functional');
        console.log('   ✅ Search functionality operational');
        console.log('   ✅ All JavaScript modules loaded');
        console.log('   ✅ All HTML pages available');
        console.log('\n💡 The website is ready for production deployment!');
    } else {
        console.log('\n⚠️  Some dynamic content issues detected. Check the failed tests above.');
        console.log('\n🔧 Troubleshooting Tips:');
        console.log('   - Ensure the backend server is running');
        console.log('   - Check database connections');
        console.log('   - Verify API endpoints are accessible');
        console.log('   - Review browser console for JavaScript errors');
    }
}

testDynamicContent();
