const OpenAI = require('openai');

// Initialize OpenAI with environment variables
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

class OpenAIService {
  constructor() {
    this.assistantId = process.env.OPENAI_ASSISTANT_ID;
  }

  async createThread() {
    try {
      const thread = await openai.beta.threads.create();
      return thread;
    } catch (error) {
      console.error('Error creating thread:', error);
      throw error;
    }
  }

  async addMessage(threadId, content) {
    try {
      const message = await openai.beta.threads.messages.create(threadId, {
        role: 'user',
        content: content
      });
      return message;
    } catch (error) {
      console.error('Error adding message:', error);
      throw error;
    }
  }

  async runAssistant(threadId) {
    try {
      const run = await openai.beta.threads.runs.create(threadId, {
        assistant_id: this.assistantId
      });
      return run;
    } catch (error) {
      console.error('Error running assistant:', error);
      throw error;
    }
  }

  async getMessages(threadId) {
    try {
      const messages = await openai.beta.threads.messages.list(threadId);
      return messages;
    } catch (error) {
      console.error('Error getting messages:', error);
      throw error;
    }
  }
}

module.exports = new OpenAIService(); 