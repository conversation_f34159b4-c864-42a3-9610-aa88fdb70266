const restaurantService = require('../services/restaurantService');

class RestaurantController {
  async getAllRestaurants(req, res) {
    try {
      const restaurants = await restaurantService.getAllRestaurants();
      res.json(restaurants);
    } catch (error) {
      console.error('Error in getAllRestaurants:', error);
      res.status(500).json({ error: error.message });
    }
  }

  async getRestaurantInfo(req, res) {
    try {
      const { restaurantId } = req.params;
      const restaurant = await restaurantService.getRestaurantInfo(restaurantId);
      res.json(restaurant);
    } catch (error) {
      console.error('Error in getRestaurantInfo:', error);
      res.status(404).json({ error: error.message });
    }
  }

  async getRestaurantWithMenu(req, res) {
    try {
      const { restaurantId } = req.params;
      const restaurant = await restaurantService.getRestaurantWithMenu(restaurantId);
      res.json(restaurant);
    } catch (error) {
      console.error('Error in getRestaurantWithMenu:', error);
      res.status(404).json({ error: error.message });
    }
  }

  async searchRestaurants(req, res) {
    try {
      const { query } = req.query;
      const restaurants = await restaurantService.searchRestaurants(query || "");
      res.json(restaurants);
    } catch (error) {
      console.error('Error in searchRestaurants:', error);
      res.status(500).json({ error: error.message });
    }
  }

  async getMenuItems(req, res) {
    try {
      const { restaurantId } = req.params;
      const menuItems = await restaurantService.getMenuItems(restaurantId);
      res.json(menuItems);
    } catch (error) {
      console.error('Error in getMenuItems:', error);
      res.status(404).json({ error: error.message });
    }
  }

  async getMenuItemsByCategory(req, res) {
    try {
      const { restaurantId } = req.params;
      const categorizedMenu = await restaurantService.getMenuItemsByCategory(restaurantId);
      res.json(categorizedMenu);
    } catch (error) {
      console.error('Error in getMenuItemsByCategory:', error);
      res.status(404).json({ error: error.message });
    }
  }

  async getFoodDeals(req, res) {
    try {
      const deals = await restaurantService.getFoodDeals();
      res.json(deals);
    } catch (error) {
      console.error('Error in getFoodDeals:', error);
      res.status(500).json({ error: error.message });
    }
  }
}

module.exports = new RestaurantController(); 