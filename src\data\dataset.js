module.exports = {
    restaurants: [
        {
            id: "pizza-point",
            name: "Pizza Point",
            contact: "N/A",
            address: "J<PERSON><PERSON>",
            rating: 4.5,
            cuisine: "Pizza",
            image: "/assets/restaurant1.jpg",
            menu: [
                { name: "Pepperoni Pizza", original: 1200, discount: 1200, category: "Pizza", description: "Classic pepperoni pizza.", image: "/assets/restaurant1.jpg" },
                { name: "Chicken Tikka Pizza", original: 1400, discount: 1400, category: "Pizza", description: "Spicy chicken tikka pizza.", image: "/assets/restaurant1.jpg" },
                { name: "BBQ Pizza", original: 1300, discount: 1300, category: "Pizza", description: "BBQ flavored pizza.", image: "/assets/restaurant1.jpg" },
                { name: "Fajita Pizza", original: 1500, discount: 1500, category: "Pizza", description: "Fajita style pizza.", image: "/assets/restaurant1.jpg" }
            ]
        },
        {
            id: "burger-lab",
            name: "Burger Lab",
            contact: "N/A",
            address: "<PERSON><PERSON><PERSON>",
            rating: 4.3,
            cuisine: "Burgers",
            image: "/assets/restaurant2.jpg",
            menu: [
                { name: "Classic Burger", original: 600, discount: 600, category: "Burgers", description: "Classic beef burger.", image: "/assets/restaurant2.jpg" },
                { name: "Chicken Burger", original: 700, discount: 700, category: "Burgers", description: "Juicy chicken burger.", image: "/assets/restaurant2.jpg" },
                { name: "BBQ Burger", original: 800, discount: 800, category: "Burgers", description: "BBQ flavored burger.", image: "/assets/restaurant2.jpg" },
                { name: "Double Patty Burger", original: 900, discount: 900, category: "Burgers", description: "Double patty burger.", image: "/assets/restaurant2.jpg" }
            ]
        },
        {
            id: "chinese-wok",
            name: "Chinese Wok",
            contact: "N/A",
            address: "Jahanian",
            rating: 4.4,
            cuisine: "Chinese",
            image: "/assets/restaurant3.jpg",
            menu: [
                { name: "Chicken Chowmein", original: 500, discount: 500, category: "Chinese", description: "Stir-fried chicken chowmein.", image: "/assets/restaurant3.jpg" },
                { name: "Fried Rice", original: 400, discount: 400, category: "Chinese", description: "Special fried rice.", image: "/assets/restaurant3.jpg" },
                { name: "Manchurian", original: 600, discount: 600, category: "Chinese", description: "Chicken manchurian.", image: "/assets/restaurant3.jpg" },
                { name: "Sweet & Sour Soup", original: 300, discount: 300, category: "Chinese", description: "Sweet and sour soup.", image: "/assets/restaurant3.jpg" }
            ]
        },
        {
            id: "desi-dhaba",
            name: "Desi Dhaba",
            contact: "N/A",
            address: "Jahanian",
            rating: 4.6,
            cuisine: "Desi",
            image: "/assets/restaurant4.jpg",
            menu: [
                { name: "Chicken Karahi", original: 1200, discount: 1200, category: "Desi", description: "Traditional chicken karahi.", image: "/assets/restaurant4.jpg" },
                { name: "Biryani", original: 300, discount: 300, category: "Desi", description: "Special biryani.", image: "/assets/restaurant4.jpg" },
                { name: "Naan", original: 40, discount: 40, category: "Desi", description: "Fresh naan.", image: "/assets/restaurant4.jpg" },
                { name: "Chicken Tikka", original: 800, discount: 800, category: "Desi", description: "Chicken tikka.", image: "/assets/restaurant4.jpg" }
            ]
        }
    ],
    categories: [
        { id: "all", name: "All", icon: "fa-utensils" },
        { id: "pizza", name: "Pizza", icon: "fa-pizza-slice" },
        { id: "burgers", name: "Burgers", icon: "fa-hamburger" },
        { id: "chinese", name: "Chinese", icon: "fa-bowl-rice" },
        { id: "desi", name: "Desi", icon: "fa-pepper-hot" },
        { id: "deals", name: "Deals", icon: "fa-tags" }
    ]
}; 