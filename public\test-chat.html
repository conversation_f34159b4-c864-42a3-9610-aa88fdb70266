<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Widget Test - Torbaaz</title>
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/chat.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 5px;
            cursor: pointer;
            margin: 0.5rem;
            font-size: 1rem;
        }
        .test-button:hover {
            background: #e55a2b;
        }
        .status {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
            border-left: 4px solid #ff6b35;
            background: #fff3f0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🤖 Chat Widget Test Page</h1>
        <p>This page is designed to test the Jarvis chat widget functionality.</p>
        
        <div class="status">
            <h3>📋 Test Instructions:</h3>
            <ol>
                <li>Open browser console (F12) to see debug messages</li>
                <li>Click the "Ask Jarvis" button in the bottom-right corner</li>
                <li>Verify that the welcome message appears</li>
                <li>Check that suggestion chips are visible and clickable</li>
                <li>Test typing in the message input</li>
            </ol>
        </div>

        <div class="test-buttons">
            <h3>🧪 Manual Test Functions:</h3>
            <button class="test-button" onclick="testChatWidget()">
                🔧 Force Test Chat Widget
            </button>
            <button class="test-button" onclick="debugChatWidget()">
                🔍 Debug Chat Widget
            </button>
            <button class="test-button" onclick="openChatWidget()">
                📂 Open Chat Widget
            </button>
            <button class="test-button" onclick="addTestMessage()">
                💬 Add Test Message
            </button>
        </div>

        <div id="testResults" class="status" style="display: none;">
            <h3>✅ Test Results:</h3>
            <div id="resultsList"></div>
        </div>
    </div>

    <!-- Chat Widget -->
    <div class="chat-widget minimized" id="chatWidget">
        <div class="chat-toggle">
            <div class="chat-title">
                <i class="fas fa-robot"></i>
                <span>Ask Jarvis</span>
            </div>
        </div>
        <div class="chat-content">
            <div class="chat-container">
                <div class="chat-messages">
                    <!-- Messages will be dynamically added here -->
                </div>
            </div>
            <div class="suggestion-chips">
                <!-- Suggestion chips will be added dynamically -->
            </div>
            <div class="chat-input">
                <textarea class="message-input" placeholder="Ask me anything..." rows="1"></textarea>
                <button class="send-button">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <script src="/js/chat.js"></script>
    <script>
        // Additional test functions
        function openChatWidget() {
            const widget = document.querySelector('.chat-widget');
            if (widget) {
                widget.classList.remove('minimized');
                console.log('✅ Chat widget opened manually');
                showResult('Chat widget opened');
            } else {
                console.error('❌ Chat widget not found');
                showResult('Chat widget not found', false);
            }
        }

        function addTestMessage() {
            if (window.chatWidget && window.chatWidget.messagesContainer) {
                const testMsg = document.createElement('div');
                testMsg.className = 'message bot-message';
                testMsg.style.cssText = `
                    margin-bottom: 1rem;
                    max-width: 80%;
                    padding: 0.8rem 1rem;
                    border-radius: 15px 15px 15px 0;
                    background: #f5f5f5;
                    margin-right: auto;
                    opacity: 1;
                    transform: translateY(0);
                `;
                testMsg.innerHTML = "🧪 Manual test message added successfully!";
                window.chatWidget.messagesContainer.appendChild(testMsg);
                console.log('✅ Test message added');
                showResult('Test message added');
            } else {
                console.error('❌ Chat widget or messages container not found');
                showResult('Failed to add test message', false);
            }
        }

        function showResult(message, success = true) {
            const resultsDiv = document.getElementById('testResults');
            const resultsList = document.getElementById('resultsList');
            
            resultsDiv.style.display = 'block';
            
            const resultItem = document.createElement('div');
            resultItem.style.cssText = `
                padding: 0.5rem;
                margin: 0.25rem 0;
                border-radius: 3px;
                background: ${success ? '#d4edda' : '#f8d7da'};
                color: ${success ? '#155724' : '#721c24'};
                border: 1px solid ${success ? '#c3e6cb' : '#f5c6cb'};
            `;
            resultItem.innerHTML = `${success ? '✅' : '❌'} ${message}`;
            
            resultsList.appendChild(resultItem);
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🚀 Running automatic tests...');
                
                // Test 1: Check if chat widget exists
                const widget = document.querySelector('.chat-widget');
                showResult(`Chat widget element: ${widget ? 'Found' : 'Not found'}`, !!widget);
                
                // Test 2: Check if chat widget instance exists
                showResult(`Chat widget instance: ${window.chatWidget ? 'Created' : 'Not created'}`, !!window.chatWidget);
                
                // Test 3: Check if messages container exists
                const messagesContainer = document.querySelector('.chat-messages');
                showResult(`Messages container: ${messagesContainer ? 'Found' : 'Not found'}`, !!messagesContainer);
                
                // Test 4: Check if suggestion chips container exists
                const suggestionsContainer = document.querySelector('.suggestion-chips');
                showResult(`Suggestions container: ${suggestionsContainer ? 'Found' : 'Not found'}`, !!suggestionsContainer);
                
            }, 1000);
        });
    </script>
</body>
</html>
