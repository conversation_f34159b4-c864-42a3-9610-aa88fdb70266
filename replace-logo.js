const fs = require('fs');
const path = require('path');

// Logo replacement utility
function replaceLogo(newLogoPath) {
    console.log('🔄 Starting logo replacement process...');
    
    // Define all files that contain logo references
    const filesToUpdate = [
        'public/index.html',
        'public/about.html',
        'public/deals.html',
        'public/cart.html',
        'public/orders.html'
    ];
    
    // Define logo references to replace
    const logoReferences = [
        { old: 'favicon.png', new: newLogoPath },
        { old: 'src="favicon.png"', new: `src="${newLogoPath}"` },
        { old: 'href="favicon.png"', new: `href="${newLogoPath}"` }
    ];
    
    let updatedFiles = 0;
    
    filesToUpdate.forEach(filePath => {
        const fullPath = path.join(__dirname, filePath);
        
        if (fs.existsSync(fullPath)) {
            let content = fs.readFileSync(fullPath, 'utf8');
            let fileUpdated = false;
            
            logoReferences.forEach(ref => {
                if (content.includes(ref.old)) {
                    content = content.replace(new RegExp(ref.old, 'g'), ref.new);
                    fileUpdated = true;
                }
            });
            
            if (fileUpdated) {
                fs.writeFileSync(fullPath, content);
                console.log(`✅ Updated logo references in: ${filePath}`);
                updatedFiles++;
            }
        } else {
            console.log(`⚠️  File not found: ${filePath}`);
        }
    });
    
    console.log(`\n🎉 Logo replacement complete! Updated ${updatedFiles} files.`);
    console.log('📝 Remember to:');
    console.log('   - Copy the new logo file to the public directory');
    console.log('   - Update any CSS background-image references if needed');
    console.log('   - Test all pages to ensure logo displays correctly');
}

// Usage example:
// replaceLogo('new-torbaaz-logo.png');

module.exports = { replaceLogo };

// If run directly
if (require.main === module) {
    console.log('Logo replacement utility ready.');
    console.log('Usage: replaceLogo("path/to/new-logo.png")');
    console.log('Note: Place the new logo file in the public directory first.');
}
