let currentThreadId = null;
let currentFilter = 'all';
let allRestaurants = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', async () => {
    // Create a new thread for the chat
    try {
        const response = await fetch('/api/assistant/threads', {
            method: 'POST'
        });
        const data = await response.json();
        currentThreadId = data.threadId;
        addMessage('Hello! I\'m your AI assistant. How can I help you find restaurants in Jahanian?', false);
    } catch (error) {
        console.error('Error creating thread:', error);
        addMessage('Sorry, there was an error initializing the chat.', false);
    }

    // Load initial restaurants
    loadRestaurants();

    // Load food deals
    loadFoodDeals();

    // Add event listeners
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchRestaurants();
        }
    });

    document.getElementById('messageInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });

    // Add scroll animations
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('show');
            }
        });
    }, { threshold: 0.1 });

    document.querySelectorAll('.restaurant-card, .deal-card').forEach((card) => {
        observer.observe(card);
    });
});

// Filter restaurants
function filterRestaurants(cuisine) {
    currentFilter = cuisine;
    const buttons = document.querySelectorAll('.filter-btn');
    buttons.forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[onclick="filterRestaurants('${cuisine}')"]`).classList.add('active');

    const filteredRestaurants = cuisine === 'all' 
        ? allRestaurants 
        : allRestaurants.filter(r => r.cuisine === cuisine);
    
    displayRestaurants(filteredRestaurants);
}

// Search restaurants
async function searchRestaurants() {
    const searchInput = document.getElementById('searchInput');
    const query = searchInput.value.trim();
    const restaurantsList = document.getElementById('restaurantsList');
    
    // Show loading state
    restaurantsList.innerHTML = '<div class="loading">Searching restaurants...</div>';
    
    try {
        const response = await fetch(`/api/restaurants/search?query=${encodeURIComponent(query)}`);
        if (!response.ok) {
            throw new Error('Failed to fetch restaurants');
        }
        const restaurants = await response.json();
        allRestaurants = restaurants; // Store all restaurants
        const filteredRestaurants = currentFilter === 'all' 
            ? restaurants 
            : restaurants.filter(r => r.cuisine === currentFilter);
        displayRestaurants(filteredRestaurants);
    } catch (error) {
        console.error('Error searching restaurants:', error);
        restaurantsList.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <p>Sorry, there was an error searching restaurants.</p>
                <button onclick="loadRestaurants()">Try Again</button>
            </div>
        `;
    }
}

// Load all restaurants
async function loadRestaurants() {
    const restaurantsList = document.getElementById('restaurantsList');
    
    // Show loading state
    restaurantsList.innerHTML = '<div class="loading">Loading restaurants...</div>';
    
    try {
        const response = await fetch('/api/restaurants/search?query=');
        if (!response.ok) {
            throw new Error('Failed to fetch restaurants');
        }
        const restaurants = await response.json();
        allRestaurants = restaurants; // Store all restaurants
        const filteredRestaurants = currentFilter === 'all' 
            ? restaurants 
            : restaurants.filter(r => r.cuisine === currentFilter);
        displayRestaurants(filteredRestaurants);
    } catch (error) {
        console.error('Error loading restaurants:', error);
        restaurantsList.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <p>Sorry, there was an error loading restaurants.</p>
                <button onclick="loadRestaurants()">Try Again</button>
            </div>
        `;
    }
}

// Display restaurants in the UI
function displayRestaurants(restaurants) {
    const restaurantsList = document.getElementById('restaurantsList');
    
    if (!Array.isArray(restaurants)) {
        console.error('Expected array of restaurants but got:', restaurants);
        restaurantsList.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <p>Sorry, there was an error with the restaurant data.</p>
                <button onclick="loadRestaurants()">Try Again</button>
            </div>
        `;
        return;
    }

    if (restaurants.length === 0) {
        restaurantsList.innerHTML = `
            <div class="no-results">
                <i class="fas fa-utensils"></i>
                <h3>No restaurants found</h3>
                <p>Try a different search term or filter</p>
            </div>
        `;
        return;
    }

    restaurantsList.innerHTML = '';
    restaurants.forEach((restaurant, index) => {
        const restaurantCard = document.createElement('div');
        restaurantCard.className = 'restaurant-card';
        restaurantCard.style.animationDelay = `${index * 0.1}s`;
        restaurantCard.innerHTML = `
            <img src="${restaurant.imageUrl || 'https://via.placeholder.com/300x200?text=No+Image'}" 
                 alt="${restaurant.name}" 
                 class="restaurant-image"
                 onerror="this.src='https://via.placeholder.com/300x200?text=No+Image'">
            <div class="restaurant-info">
                <h3>${restaurant.name}</h3>
                <p><i class="fas fa-utensils"></i> ${restaurant.cuisine}</p>
                <p><i class="fas fa-map-marker-alt"></i> ${restaurant.location}</p>
                <p class="rating">
                    <i class="fas fa-star"></i> ${restaurant.rating} 
                    ${Array(Math.round(restaurant.rating)).fill('⭐').join('')}
                </p>
                <p><i class="fas fa-clock"></i> ${restaurant.openingHours}</p>
                <button class="view-menu-btn" onclick="showRestaurantDetails(${restaurant.id})">
                    <i class="fas fa-clipboard-list"></i> View Menu
                </button>
            </div>
        `;
        restaurantsList.appendChild(restaurantCard);
    });
}

// Show restaurant details
async function showRestaurantDetails(restaurantId) {
    try {
        const response = await fetch(`/api/restaurants/${restaurantId}`);
        if (!response.ok) {
            throw new Error('Failed to fetch restaurant details');
        }
        const restaurant = await response.json();
        
        const menuResponse = await fetch(`/api/restaurants/${restaurantId}/menu`);
        if (!menuResponse.ok) {
            throw new Error('Failed to fetch menu items');
        }
        const menu = await menuResponse.json();

        const message = `
            <div class="restaurant-details">
                <h3>${restaurant.name}</h3>
                <p><strong><i class="fas fa-utensils"></i> Cuisine:</strong> ${restaurant.cuisine}</p>
                <p><strong><i class="fas fa-map-marker-alt"></i> Location:</strong> ${restaurant.location}</p>
                <p><strong><i class="fas fa-star"></i> Rating:</strong> ${restaurant.rating} ${Array(Math.round(restaurant.rating)).fill('⭐').join('')}</p>
                <p><strong><i class="fas fa-clock"></i> Hours:</strong> ${restaurant.openingHours}</p>
                
                <h4><i class="fas fa-clipboard-list"></i> Menu Items:</h4>
                <div class="menu-items">
                    ${menu.map(item => `
                        <div class="menu-item">
                            <h5>${item.name}</h5>
                            <p class="price">Rs. ${item.price}</p>
                            <p class="description">${item.description}</p>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        addMessage(message, false);
    } catch (error) {
        console.error('Error fetching restaurant details:', error);
        addMessage('Sorry, there was an error fetching restaurant details.', false);
    }
}

// Send message to the assistant
async function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();
    
    if (!message || !currentThreadId) return;

    addMessage(message, true);
    messageInput.value = '';

    try {
        const response = await fetch('/api/assistant/messages', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                threadId: currentThreadId,
                message: message
            })
        });

        if (!response.ok) {
            throw new Error('Failed to send message');
        }

        const data = await response.json();
        
        // Get the latest assistant message
        const messages = data.messages;
        const assistantMessage = messages.find(m => m.role === 'assistant');
        
        if (assistantMessage) {
            // Handle the message content which is an array of content blocks
            const content = assistantMessage.content[0];
            if (content.type === 'text') {
                addMessage(content.text.value, false);
            }
        }
    } catch (error) {
        console.error('Error sending message:', error);
        addMessage('Sorry, there was an error processing your message.', false);
    }
}

// Add message to the chat
function addMessage(text, isUser) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isUser ? 'user-message' : 'assistant-message'}`;
    messageDiv.innerHTML = text;
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Load food deals
async function loadFoodDeals() {
    const dealsContainer = document.getElementById('dealsContainer');
    
    // Show loading state
    dealsContainer.innerHTML = `
        <div class="loading-deals">
            <i class="fas fa-spinner"></i>
            <p>Loading today's special deals...</p>
        </div>
    `;
    
    try {
        const response = await fetch('/api/restaurants/deals/food');
        if (!response.ok) {
            throw new Error('Failed to fetch deals');
        }
        const deals = await response.json();
        displayDeals(deals);
    } catch (error) {
        console.error('Error loading deals:', error);
        dealsContainer.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-circle"></i>
                <p>Sorry, there was an error loading the deals.</p>
                <button onclick="loadFoodDeals()">Try Again</button>
            </div>
        `;
    }
}

// Display deals in the UI
function displayDeals(deals) {
    const dealsContainer = document.getElementById('dealsContainer');
    
    if (!Array.isArray(deals) || deals.length === 0) {
        dealsContainer.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-circle"></i>
                <p>No special deals available at the moment.</p>
            </div>
        `;
        return;
    }

    dealsContainer.innerHTML = '';
    deals.forEach((deal, index) => {
        const dealCard = document.createElement('div');
        dealCard.className = 'deal-card';
        dealCard.style.animationDelay = `${index * 0.1}s`;
        
        const validUntil = new Date(deal.validUntil);
        const formattedDate = validUntil.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });

        dealCard.innerHTML = `
            <h3>${deal.name}</h3>
            <p>From ${deal.restaurantName}</p>
            <div class="deal-price">
                <span class="original-price">Rs. ${deal.originalPrice}</span>
                <span class="discounted-price">Rs. ${deal.discountedPrice}</span>
            </div>
            <p>${deal.description}</p>
            <p class="valid-until">Valid until ${formattedDate}</p>
            <button class="view-menu-btn" onclick="showRestaurantDetails(${deal.restaurantId})">
                <i class="fas fa-utensils"></i> View Restaurant
            </button>
        `;
        dealsContainer.appendChild(dealCard);
    });
}

// Show About Page
function showAboutPage() {
    const content = `
        <div class="about-page">
            <h2><i class="fas fa-info-circle"></i> About Jahanian Food Menu</h2>
            <p>Welcome to Jahanian Food Menu, your ultimate guide to discovering the best restaurants in Jahanian. Our platform combines traditional dining with modern technology to provide you with a seamless food discovery experience.</p>
            
            <h3>Our Features</h3>
            <ul>
                <li>AI-powered restaurant recommendations</li>
                <li>Real-time menu updates</li>
                <li>Exclusive deals and discounts</li>
                <li>Easy restaurant search and filtering</li>
            </ul>
            
            <h3>Our Mission</h3>
            <p>We aim to connect food lovers with the best dining experiences in Jahanian while supporting local restaurants and businesses.</p>
        </div>
    `;
    addMessage(content, false);
}

// Show Feedback Page
function showFeedbackPage() {
    const content = `
        <div class="feedback-page">
            <h2><i class="fas fa-comment-alt"></i> Share Your Feedback</h2>
            <p>We value your opinion! Please share your thoughts about our service.</p>
            
            <div class="feedback-form">
                <textarea placeholder="Your feedback here..." rows="4"></textarea>
                <div class="rating">
                    <span class="star" onclick="rateApp(1)">⭐</span>
                    <span class="star" onclick="rateApp(2)">⭐</span>
                    <span class="star" onclick="rateApp(3)">⭐</span>
                    <span class="star" onclick="rateApp(4)">⭐</span>
                    <span class="star" onclick="rateApp(5)">⭐</span>
                </div>
                <button onclick="submitFeedback()">Submit Feedback</button>
            </div>
        </div>
    `;
    addMessage(content, false);
}

// Show Privacy Policy
function showPrivacyPolicy() {
    const content = `
        <div class="privacy-policy">
            <h2><i class="fas fa-shield-alt"></i> Privacy Policy</h2>
            <p>At Jahanian Food Menu, we take your privacy seriously. This policy outlines how we collect, use, and protect your personal information.</p>
            
            <h3>Data Collection</h3>
            <p>We collect minimal data necessary to provide our services, including:</p>
            <ul>
                <li>Search history for better recommendations</li>
                <li>Device information for optimization</li>
                <li>Location data for local results</li>
            </ul>
            
            <h3>Data Protection</h3>
            <p>Your data is encrypted and stored securely. We never share your personal information with third parties without your consent.</p>
        </div>
    `;
    addMessage(content, false);
} 