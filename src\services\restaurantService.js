const supabase = require('../config/supabase');

class RestaurantService {
  async getAllRestaurants() {
    try {
      const { data, error } = await supabase
        .from('restaurants')
        .select('*')
        .order('rating', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching restaurants:', error);
      throw error;
    }
  }

  async getRestaurantInfo(restaurantId) {
    try {
      const { data, error } = await supabase
        .from('restaurants')
        .select('*')
        .eq('id', restaurantId)
        .single();

      if (error) throw error;
      if (!data) throw new Error('Restaurant not found');

      return data;
    } catch (error) {
      console.error('Error fetching restaurant info:', error);
      throw error;
    }
  }

  async searchRestaurants(query = "") {
    try {
      if (!query) {
        return await this.getAllRestaurants();
      }

      const { data, error } = await supabase
        .from('restaurants')
        .select('*')
        .ilike('name', `%${query}%`)
        .order('rating', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error searching restaurants:', error);
      throw error;
    }
  }

  async getMenuItems(restaurantId) {
    try {
      const { data, error } = await supabase
        .from('menu_items')
        .select(`
          *,
          categories (
            name
          )
        `)
        .eq('restaurant_id', restaurantId)
        .eq('is_available', true)
        .order('name', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching menu items:', error);
      throw error;
    }
  }

  async getRestaurantWithMenu(restaurantId) {
    try {
      const restaurant = await this.getRestaurantInfo(restaurantId);
      const menuItems = await this.getMenuItems(restaurantId);

      return {
        ...restaurant,
        menu: menuItems
      };
    } catch (error) {
      console.error('Error fetching restaurant with menu:', error);
      throw error;
    }
  }

  async getFoodDeals() {
    try {
      // Get deals from the deals table
      const { data, error } = await supabase
        .from('deals')
        .select(`
          *,
          restaurants (
            name,
            logo_url
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching food deals:', error);
      throw error;
    }
  }

  async getMenuItemsByCategory(restaurantId) {
    try {
      const menuItems = await this.getMenuItems(restaurantId);
      const categorized = {};

      menuItems.forEach(item => {
        if (!categorized[item.category]) {
          categorized[item.category] = [];
        }
        categorized[item.category].push(item);
      });

      return categorized;
    } catch (error) {
      console.error('Error categorizing menu items:', error);
      throw error;
    }
  }
}

module.exports = new RestaurantService(); 