const supabase = require('../config/supabase');
const fs = require('fs');
const path = require('path');

class DataSeeder {
  constructor() {
    this.restaurantData = this.parseRestaurantData();
  }

  parseRestaurantData() {
    try {
      const dataPath = path.join(__dirname, '../../data/ai_assistant_data.txt');
      const data = fs.readFileSync(dataPath, 'utf8');
      const lines = data.split('\n').filter(line => line.trim());
      
      const restaurants = [];
      let currentRestaurant = null;
      
      for (const line of lines) {
        if (line.startsWith('Restaurant:')) {
          if (currentRestaurant) {
            restaurants.push(currentRestaurant);
          }
          const name = line.replace('Restaurant:', '').trim();
          currentRestaurant = {
            name,
            description: `Delicious ${name} cuisine`,
            image_url: this.getRestaurantImage(name),
            rating: (4.0 + Math.random() * 1.0).toFixed(1),
            delivery_time: `${20 + Math.floor(Math.random() * 30)}-${40 + Math.floor(Math.random() * 20)} min`,
            delivery_fee: Math.floor(Math.random() * 100) + 30,
            minimum_order: Math.floor(Math.random() * 300) + 200,
            menu_items: []
          };
        } else if (line.includes(' - Rs.')) {
          const [itemName, priceStr] = line.split(' - Rs.');
          const price = parseFloat(priceStr.trim());
          const category = this.categorizeItem(itemName);
          
          currentRestaurant.menu_items.push({
            name: itemName.trim(),
            description: `Delicious ${itemName.toLowerCase()}`,
            price,
            category,
            is_available: true
          });
        }
      }
      
      if (currentRestaurant) {
        restaurants.push(currentRestaurant);
      }
      
      return restaurants;
    } catch (error) {
      console.error('Error parsing restaurant data:', error);
      return [];
    }
  }

  getRestaurantImage(name) {
    const imageMap = {
      'Pizza Point': '/assets/restaurant1.jpg',
      'Burger Lab': '/assets/restaurant2.jpg',
      'Chinese Wok': '/assets/restaurant3.jpg',
      'Desi Dhaba': '/assets/restaurant4.jpg'
    };
    return imageMap[name] || '/assets/restaurant1.jpg';
  }

  categorizeItem(itemName) {
    const name = itemName.toLowerCase();
    if (name.includes('pizza')) return 'Pizza';
    if (name.includes('burger')) return 'Burgers';
    if (name.includes('chowmein') || name.includes('noodles')) return 'Noodles';
    if (name.includes('rice') || name.includes('biryani')) return 'Rice';
    if (name.includes('soup')) return 'Soups';
    if (name.includes('karahi') || name.includes('tikka')) return 'Main Course';
    if (name.includes('naan') || name.includes('bread')) return 'Bread';
    if (name.includes('manchurian')) return 'Chicken';
    return 'Main Course';
  }

  async seedDatabase() {
    try {
      console.log('Starting database seeding...');
      
      // Clear existing data
      await this.clearExistingData();
      
      // Insert restaurants and menu items
      for (const restaurantData of this.restaurantData) {
        await this.insertRestaurant(restaurantData);
      }
      
      console.log('Database seeding completed successfully!');
      return true;
    } catch (error) {
      console.error('Error seeding database:', error);
      return false;
    }
  }

  async clearExistingData() {
    console.log('Clearing existing data...');
    
    // Delete in correct order due to foreign key constraints
    await supabase.from('order_items').delete().neq('id', 0);
    await supabase.from('orders').delete().neq('id', 0);
    await supabase.from('favorites').delete().neq('id', 0);
    await supabase.from('menu_items').delete().neq('id', 0);
    await supabase.from('restaurants').delete().neq('id', 0);
    
    console.log('Existing data cleared.');
  }

  async insertRestaurant(restaurantData) {
    try {
      // Insert restaurant
      const { data: restaurant, error: restaurantError } = await supabase
        .from('restaurants')
        .insert({
          name: restaurantData.name,
          description: restaurantData.description,
          image_url: restaurantData.image_url,
          rating: parseFloat(restaurantData.rating),
          delivery_time: restaurantData.delivery_time,
          delivery_fee: restaurantData.delivery_fee,
          minimum_order: restaurantData.minimum_order,
          is_active: true
        })
        .select()
        .single();

      if (restaurantError) {
        console.error('Error inserting restaurant:', restaurantError);
        return;
      }

      console.log(`Inserted restaurant: ${restaurant.name}`);

      // Insert menu items
      for (const item of restaurantData.menu_items) {
        const { error: itemError } = await supabase
          .from('menu_items')
          .insert({
            restaurant_id: restaurant.id,
            name: item.name,
            description: item.description,
            price: item.price,
            category: item.category,
            is_available: item.is_available
          });

        if (itemError) {
          console.error('Error inserting menu item:', itemError);
        }
      }

      console.log(`Inserted ${restaurantData.menu_items.length} menu items for ${restaurant.name}`);
    } catch (error) {
      console.error('Error in insertRestaurant:', error);
    }
  }

  async checkConnection() {
    try {
      const { data, error } = await supabase
        .from('restaurants')
        .select('count')
        .limit(1);
      
      if (error) {
        console.error('Supabase connection error:', error);
        return false;
      }
      
      console.log('Supabase connection successful');
      return true;
    } catch (error) {
      console.error('Error checking Supabase connection:', error);
      return false;
    }
  }
}

module.exports = DataSeeder;
