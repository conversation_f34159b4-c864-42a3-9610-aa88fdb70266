const { FavoritesService } = require('../services/favoritesService');

async function manageFavorites(userId, restaurantId, itemId, action) {
    try {
        const favoritesService = new FavoritesService(userId);
        
        if (action === 'add') {
            await favoritesService.addFavorite(restaurantId, itemId);
            return { success: true, message: 'Item added to favorites' };
        } else if (action === 'remove') {
            await favoritesService.removeFavorite(restaurantId, itemId);
            return { success: true, message: 'Item removed from favorites' };
        } else {
            throw new Error('Invalid action. Must be either "add" or "remove"');
        }
    } catch (error) {
        console.error('Error managing favorites:', error);
        throw error;
    }
}

module.exports = { manageFavorites };
