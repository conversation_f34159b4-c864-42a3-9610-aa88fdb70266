// Chat widget functionality
class ChatWidget {
    constructor() {
        console.log('🤖 Initializing Chat Widget...');

        // Find all required elements
        this.widget = document.querySelector('.chat-widget');
        this.container = document.querySelector('.chat-container');
        this.input = document.querySelector('.message-input');
        this.sendButton = document.querySelector('.send-button');
        this.toggleButton = document.querySelector('.chat-toggle');
        this.suggestionChips = document.querySelector('.suggestion-chips');

        if (!this.widget) {
            console.error('❌ Chat widget not found');
            return;
        }

        // Ensure messages container exists
        this.ensureMessagesContainer();

        // Generate a random user ID for this session
        this.userId = 'user_' + Math.random().toString(36).substring(2, 15);

        // Initialize drag and resize properties
        this.initializeDragProperties();

        // Initialize components
        this.setupEventListeners();
        this.setupDragAndResize();

        // Force initialize content with multiple attempts
        this.forceInitializeContent();

        // Set initial state to collapsed
        this.initializeDefaultState();

        console.log('✅ Enhanced chat widget initialized successfully');
    }

    initializeDefaultState() {
        // Set default collapsed state
        this.widget.classList.add('chat-collapsed');
        this.widget.classList.remove('chat-expanded', 'maximized');
        this.isExpanded = false;
        this.isMaximized = false;
        this.isMinimized = false;

        // Ensure proper button icons
        const maximizeBtn = this.widget.querySelector('.maximize-btn i');
        if (maximizeBtn) {
            maximizeBtn.className = 'fas fa-expand';
        }

        const minimizeBtn = this.widget.querySelector('.minimize-btn i');
        if (minimizeBtn) {
            minimizeBtn.className = 'fas fa-minus';
            minimizeBtn.parentElement.title = 'Minimize';
        }

        console.log('✅ Default collapsed state initialized');
    }

    initializeDragProperties() {
        // Enhanced state management
        this.isDragging = false;
        this.isResizing = false;
        this.isMaximized = false;
        this.isExpanded = false;
        this.isMinimized = false;
        this.animationInProgress = false;

        // State before minimizing (to restore to)
        this.stateBeforeMinimize = {
            wasExpanded: false,
            wasCollapsed: true
        };

        // Position tracking
        this.dragOffset = { x: 0, y: 0 };
        this.dragStartTime = 0;
        this.currentPosition = { left: null, top: null, right: '20px', bottom: '20px' };
        this.originalPosition = { bottom: '20px', right: '20px', width: '350px', height: '500px' };
        this.expandedPosition = { top: '5vh', left: '5vw', width: '90vw', height: '90vh' };

        // Resize tracking
        this.resizeStartX = 0;
        this.resizeStartY = 0;
        this.resizeStartWidth = 0;
        this.resizeStartHeight = 0;

        // Animation settings
        this.animationDuration = 400; // ms
        this.scrollPosition = 0;

        // Create backdrop element
        this.createBackdrop();

        console.log('✅ Enhanced drag properties initialized');
    }

    createBackdrop() {
        // Create backdrop element for modal effect
        this.backdrop = document.createElement('div');
        this.backdrop.className = 'chat-backdrop';
        this.backdrop.addEventListener('click', () => {
            if (this.isExpanded) {
                this.toggleExpandCollapse();
            }
        });
        document.body.appendChild(this.backdrop);
        console.log('✅ Backdrop created');
    }

    preserveScrollPosition() {
        const messagesContainer = this.widget.querySelector('.chat-messages');
        if (messagesContainer) {
            this.scrollPosition = messagesContainer.scrollTop;
        }
    }

    restoreScrollPosition() {
        const messagesContainer = this.widget.querySelector('.chat-messages');
        if (messagesContainer && this.scrollPosition !== undefined) {
            setTimeout(() => {
                messagesContainer.scrollTop = this.scrollPosition;
            }, 50);
        }
    }

    forceInitializeContent() {
        console.log('🚀 Force initializing content...');

        // Immediate initialization
        this.ensureMessagesContainer();
        this.forceAddWelcomeMessage();

        // Backup initialization after 100ms
        setTimeout(() => {
            console.log('🔄 Backup content initialization...');
            this.ensureMessagesContainer();
            if (!this.messagesContainer || this.messagesContainer.children.length === 0) {
                this.forceAddWelcomeMessage();
            }
        }, 100);

        // Final backup after 500ms
        setTimeout(() => {
            console.log('🔄 Final backup content initialization...');
            if (!this.messagesContainer || this.messagesContainer.children.length === 0) {
                this.ensureMessagesContainer();
                this.forceAddWelcomeMessage();
            }
        }, 500);
    }

    ensureMessagesContainer() {
        // Find or create messages container
        let messagesContainer = document.querySelector('.chat-messages');

        if (!messagesContainer) {
            console.log('📝 Creating messages container...');
            messagesContainer = document.createElement('div');
            messagesContainer.className = 'chat-messages';
            messagesContainer.style.cssText = `
                flex: 1 !important;
                overflow-y: auto !important;
                padding: 1rem !important;
                display: flex !important;
                flex-direction: column !important;
                gap: 0.5rem !important;
                min-height: 200px !important;
                background: #fafafa !important;
                border-radius: 8px !important;
                margin: 0.5rem !important;
                opacity: 1 !important;
                visibility: visible !important;
            `;

            // Insert at the beginning of chat-container
            if (this.container) {
                this.container.insertBefore(messagesContainer, this.container.firstChild);
            } else {
                // Fallback: find chat-content and insert there
                const chatContent = document.querySelector('.chat-content');
                if (chatContent) {
                    chatContent.insertBefore(messagesContainer, chatContent.firstChild);
                }
            }
        } else {
            // Ensure existing container is visible
            messagesContainer.style.setProperty('display', 'flex', 'important');
            messagesContainer.style.setProperty('opacity', '1', 'important');
            messagesContainer.style.setProperty('visibility', 'visible', 'important');
        }

        this.messagesContainer = messagesContainer;
        console.log('✅ Messages container ready:', this.messagesContainer);
    }

    setupEventListeners() {
        console.log('🎯 Setting up event listeners...');

        // Toggle chat widget - add click listener to the chat title area only
        const chatTitle = this.toggleButton.querySelector('.chat-title');
        if (chatTitle) {
            chatTitle.addEventListener('click', (e) => {
                e.stopPropagation();
                // Only toggle if not dragging and drag didn't just finish
                const timeSinceLastDrag = Date.now() - this.dragStartTime;
                if (!this.isDragging && timeSinceLastDrag > 200) {
                    console.log('🔄 Chat title clicked - toggling widget');
                    this.toggleChatWidget();
                }
            });
        }

        // Ensure input and send button exist
        this.ensureInputElements();

        // Send message on button click
        if (this.sendButton) {
            this.sendButton.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('📤 Send button clicked');
                this.sendMessage();
            });
        }

        // Send message on Enter key (but create new line on Shift+Enter)
        if (this.input) {
            this.input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    console.log('⌨️ Enter key pressed');
                    this.sendMessage();
                }
            });

            // Test input functionality
            this.input.addEventListener('input', (e) => {
                console.log('✏️ Input detected:', e.target.value);
            });
        }

        // Control buttons
        this.setupControlButtons();

        console.log('✅ Event listeners set up successfully');
    }

    ensureInputElements() {
        // Find input and send button
        this.input = document.querySelector('.message-input');
        this.sendButton = document.querySelector('.send-button');

        if (!this.input) {
            console.warn('⚠️ Message input not found');
        } else {
            // Ensure input is enabled and focusable
            this.input.disabled = false;
            this.input.readOnly = false;
            this.input.style.pointerEvents = 'auto';
            console.log('✅ Message input found and enabled');
        }

        if (!this.sendButton) {
            console.warn('⚠️ Send button not found');
        } else {
            console.log('✅ Send button found');
        }
    }

    setupControlButtons() {
        console.log('🔧 Setting up enhanced control buttons...');

        // Find existing control buttons
        const controls = this.toggleButton.querySelector('.chat-controls');

        if (controls) {
            // Enhanced maximize button (now expand/collapse)
            const maximizeBtn = controls.querySelector('.maximize-btn');
            const minimizeBtn = controls.querySelector('.minimize-btn');

            if (maximizeBtn) {
                maximizeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    console.log('🔍 Expand/Collapse button clicked');
                    this.toggleExpandCollapse();
                });
                console.log('✅ Enhanced expand/collapse button listener added');
            }

            if (minimizeBtn) {
                minimizeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    console.log('➖ Minimize/Restore button clicked');
                    this.toggleMinimize();
                });
                console.log('✅ Enhanced minimize/restore button listener added');
            }

            // Add close button for expanded state
            this.addCloseButton(controls);
        } else {
            console.warn('⚠️ Control buttons not found in HTML');
        }

        // Add keyboard support
        this.setupKeyboardSupport();
    }

    addCloseButton(controls) {
        // Create close button for expanded state
        const closeBtn = document.createElement('button');
        closeBtn.className = 'chat-control-btn chat-close-btn';
        closeBtn.title = 'Close';
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';

        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            console.log('❌ Close button clicked');
            if (this.isExpanded) {
                this.toggleExpandCollapse();
            }
        });

        controls.appendChild(closeBtn);
        console.log('✅ Close button added');
    }

    setupKeyboardSupport() {
        // ESC key support for collapsing expanded chat
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isExpanded && !this.animationInProgress) {
                console.log('⌨️ ESC key pressed - collapsing chat');
                this.toggleExpandCollapse();
            }
        });
        console.log('✅ Keyboard support added');
    }

    toggleExpandCollapse() {
        if (this.animationInProgress) {
            console.log('⏳ Animation in progress, ignoring toggle request');
            return;
        }

        // If minimized, first restore then expand
        if (this.isMinimized) {
            console.log('📂 Widget is minimized, restoring first...');
            this.restoreFromMinimized();
            // Wait for restore animation to complete, then expand
            setTimeout(() => {
                if (!this.isExpanded) {
                    this.expandWidget();
                }
            }, this.animationDuration + 100);
            return;
        }

        console.log('🔄 Toggling expand/collapse state...');
        this.animationInProgress = true;

        // Preserve scroll position
        this.preserveScrollPosition();

        if (this.isExpanded) {
            // Collapse to normal size
            console.log('📉 Collapsing to normal size...');
            this.collapseWidget();
        } else {
            // Expand to full screen
            console.log('📈 Expanding to full screen...');
            this.expandWidget();
        }

        // Reset animation flag after animation completes
        setTimeout(() => {
            this.animationInProgress = false;
            this.restoreScrollPosition();
            console.log('✅ Animation completed');
        }, this.animationDuration + 50);
    }

    expandWidget() {
        console.log('🚀 Expanding widget...');

        // Show backdrop
        this.backdrop.classList.add('active');

        // Add expanded class and remove collapsed class
        this.widget.classList.add('chat-expanded');
        this.widget.classList.remove('chat-collapsed');

        // Update button icon
        const maximizeBtn = this.widget.querySelector('.maximize-btn i');
        if (maximizeBtn) {
            maximizeBtn.className = 'fas fa-compress';
        }

        this.isExpanded = true;
        console.log('✅ Widget expanded');
    }

    collapseWidget() {
        console.log('📉 Collapsing widget...');

        // Hide backdrop
        this.backdrop.classList.remove('active');

        // Add collapsed class and remove expanded class
        this.widget.classList.add('chat-collapsed');
        this.widget.classList.remove('chat-expanded');

        // Update button icon
        const maximizeBtn = this.widget.querySelector('.maximize-btn i');
        if (maximizeBtn) {
            maximizeBtn.className = 'fas fa-expand';
        }

        this.isExpanded = false;
        console.log('✅ Widget collapsed');
    }

    forceAddWelcomeMessage() {
        console.log('🎉 Force adding welcome message...');

        // Ensure messages container exists
        if (!this.messagesContainer) {
            this.ensureMessagesContainer();
        }

        // Clear any existing messages
        if (this.messagesContainer) {
            this.messagesContainer.innerHTML = '';

            // Add welcome message directly with inline styles
            const welcomeMessage = document.createElement('div');
            welcomeMessage.className = 'message bot-message';
            welcomeMessage.style.cssText = `
                margin-bottom: 1rem;
                max-width: 80%;
                padding: 0.8rem 1rem;
                border-radius: 15px 15px 15px 0;
                background: #f5f5f5;
                margin-right: auto;
                opacity: 1;
                transform: translateY(0);
            `;
            welcomeMessage.innerHTML = "Hi! I'm Jarvis, your food assistant. 🍕 How can I help you today?";

            this.messagesContainer.appendChild(welcomeMessage);
            console.log('✅ Welcome message added directly');
        }

        // Add suggestion chips
        this.forceAddSuggestionChips([
            "Show me today's deals",
            "Find me a restaurant",
            "What's on special today?",
            "Help me place an order"
        ]);
    }

    toggleChatWidget() {
        console.log('🔄 Toggling chat widget...');

        // Check if currently minimized using our state flag
        if (this.isMinimized) {
            // Restore from minimized state
            console.log('📂 Restoring chat widget from minimized...');
            this.restoreFromMinimized();
        } else {
            // This is called when clicking the chat title
            // If not minimized, we should expand the content (not minimize)
            console.log('📂 Expanding chat widget content...');

            // Remove the old minimized class if it exists
            this.widget.classList.remove('minimized');

            // Force content visibility with !important overrides
            const chatContent = this.widget.querySelector('.chat-content');
            if (chatContent) {
                chatContent.style.setProperty('display', 'flex', 'important');
                chatContent.style.setProperty('opacity', '1', 'important');
                chatContent.style.setProperty('transform', 'translateY(0)', 'important');
                chatContent.style.setProperty('visibility', 'visible', 'important');
                chatContent.style.setProperty('height', 'calc(100% - 60px)', 'important');
            }

            // Force ensure messages container exists and is visible
            this.ensureMessagesContainer();

            // Force content visibility immediately and with backup
            this.forceContentVisibility();

            // Always add welcome message when expanding
            setTimeout(() => {
                this.forceAddWelcomeMessage();
            }, 50);

            console.log('✅ Chat widget content expanded');
        }
    }

    forceContentVisibility() {
        console.log('👁️ Forcing content visibility...');

        // Force all content elements to be visible
        const chatContent = document.querySelector('.chat-content');
        const chatContainer = document.querySelector('.chat-container');
        const chatMessages = document.querySelector('.chat-messages');
        const suggestionChips = document.querySelector('.suggestion-chips');
        const chatInput = document.querySelector('.chat-input');

        const elements = [chatContent, chatContainer, chatMessages, suggestionChips, chatInput];

        elements.forEach((element, index) => {
            if (element) {
                element.style.setProperty('display', 'flex', 'important');
                element.style.setProperty('opacity', '1', 'important');
                element.style.setProperty('visibility', 'visible', 'important');
                element.style.setProperty('transform', 'translateY(0)', 'important');

                // Special handling for messages container
                if (element.classList.contains('chat-messages')) {
                    element.style.setProperty('flex-direction', 'column', 'important');
                    element.style.setProperty('min-height', '200px', 'important');
                    element.style.setProperty('background', '#fafafa', 'important');
                }

                console.log(`✅ Element ${index} forced visible:`, element.className);
            } else {
                console.warn(`⚠️ Element ${index} not found`);
            }
        });
    }

    addWelcomeMessage() {
        this.forceAddWelcomeMessage();
    }

    forceAddSuggestionChips(chips) {
        console.log('💡 Force adding suggestion chips...');

        // Find or create suggestion chips container
        let suggestionContainer = this.suggestionChips || document.querySelector('.suggestion-chips');

        if (!suggestionContainer) {
            console.log('📝 Creating suggestion chips container...');
            suggestionContainer = document.createElement('div');
            suggestionContainer.className = 'suggestion-chips';
            suggestionContainer.style.cssText = `
                padding: 1rem;
                display: flex;
                gap: 0.5rem;
                flex-wrap: wrap;
                border-top: 1px solid #eee;
            `;

            // Insert before chat-input
            const chatInput = document.querySelector('.chat-input');
            if (chatInput && chatInput.parentNode) {
                chatInput.parentNode.insertBefore(suggestionContainer, chatInput);
            }
        }

        suggestionContainer.innerHTML = '';

        chips.forEach(chip => {
            const chipElement = document.createElement('button');
            chipElement.className = 'suggestion-chip';
            chipElement.textContent = chip;
            chipElement.style.cssText = `
                background: #f5f5f5;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-size: 0.9rem;
                cursor: pointer;
                transition: background 0.3s ease;
            `;

            chipElement.addEventListener('click', () => {
                if (this.input) {
                    this.input.value = chip;
                    this.sendMessage();
                }
            });

            chipElement.addEventListener('mouseover', () => {
                chipElement.style.background = '#e0e0e0';
            });

            chipElement.addEventListener('mouseout', () => {
                chipElement.style.background = '#f5f5f5';
            });

            suggestionContainer.appendChild(chipElement);
        });

        this.suggestionChips = suggestionContainer;
        console.log(`✅ Added ${chips.length} suggestion chips`);
    }

    addSuggestionChips(chips) {
        this.forceAddSuggestionChips(chips);
    }

    addMessage(text, sender) {
        if (!this.messagesContainer) {
            console.error('❌ Messages container not found');
            return;
        }

        console.log(`💬 Adding ${sender} message: ${text.substring(0, 50)}...`);

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        // Process text for highlights
        text = this.processMessageText(text);

        messageDiv.innerHTML = text;
        this.messagesContainer.appendChild(messageDiv);

        // Scroll to bottom
        this.scrollToBottom();

        console.log('✅ Message added to chat');
    }

    processMessageText(text) {
        // Highlight prices (Rs. format)
        text = text.replace(/Rs\.?\s*\d+(\.\d{2})?/g, match => `<span class="highlight-price">${match}</span>`);

        // Highlight restaurant names
        const restaurants = ['Pizza Point', 'Burger Lab', 'Chinese Wok', 'Desi Dhaba'];
        restaurants.forEach(restaurant => {
            const regex = new RegExp(restaurant, 'gi');
            text = text.replace(regex, match => `<span class="highlight-restaurant">${match}</span>`);
        });

        return text;
    }

    showTypingIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'typing-indicator';
        indicator.innerHTML = '<span></span><span></span><span></span>';
        this.messagesContainer.appendChild(indicator);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const indicator = this.messagesContainer.querySelector('.typing-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    scrollToBottom() {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }

    async sendMessage() {
        const message = this.input.value.trim();
        if (!message) return;

        // Add user message to UI
        this.addMessage(message, 'user');
        
        // Clear input
        this.input.value = '';


        // Show typing indicator
        this.showTypingIndicator();

        try {
            // Send message to server
            const response = await fetch('/api/chat/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    userId: this.userId,
                    text: message
                })
            });

            if (!response.ok) {
                throw new Error('Failed to get response');
            }

            const data = await response.json();

            // Hide typing indicator
            this.hideTypingIndicator();

            // Add bot response to chat
            this.addMessage(data.reply, 'bot');

        } catch (error) {
            console.error('Chat error:', error);
            this.hideTypingIndicator();
            this.addMessage('Sorry, I encountered an error. Please try again.', 'bot');
        }
    }

    setupDragAndResize() {
        console.log('🎯 Setting up drag and resize functionality...');

        // Add resize handle if it doesn't exist
        let resizeHandle = this.widget.querySelector('.resize-handle');
        if (!resizeHandle) {
            resizeHandle = document.createElement('div');
            resizeHandle.className = 'resize-handle';
            resizeHandle.title = 'Drag to resize';
            this.widget.appendChild(resizeHandle);
        }

        // Enhanced drag functionality - only on chat title area
        const chatTitle = this.toggleButton.querySelector('.chat-title');
        if (chatTitle) {
            // Add cursor style
            chatTitle.style.cursor = 'move';

            chatTitle.addEventListener('mousedown', (e) => {
                // Don't start drag if clicking on control buttons
                if (e.target.closest('.chat-control-btn')) return;
                this.startDrag(e);
            });

            // Touch support for mobile
            chatTitle.addEventListener('touchstart', (e) => {
                if (e.target.closest('.chat-control-btn')) return;
                this.startDrag(e.touches[0]);
            });
        }

        // Enhanced resize functionality
        resizeHandle.addEventListener('mousedown', (e) => {
            e.stopPropagation();
            this.startResize(e);
        });

        resizeHandle.addEventListener('touchstart', (e) => {
            e.stopPropagation();
            this.startResize(e.touches[0]);
        });

        // Global mouse and touch events
        document.addEventListener('mousemove', (e) => {
            if (this.isDragging) this.drag(e);
            if (this.isResizing) this.resize(e);
        });

        document.addEventListener('touchmove', (e) => {
            if (this.isDragging) {
                e.preventDefault();
                this.drag(e.touches[0]);
            }
            if (this.isResizing) {
                e.preventDefault();
                this.resize(e.touches[0]);
            }
        });

        document.addEventListener('mouseup', () => {
            this.stopDrag();
            this.stopResize();
        });

        document.addEventListener('touchend', () => {
            this.stopDrag();
            this.stopResize();
        });

        console.log('✅ Drag and resize functionality set up');
    }

    startDrag(e) {
        // Don't drag if maximized, but allow dragging when minimized
        if (this.isMaximized) return;

        console.log('🎯 Starting drag...');
        this.isDragging = true;
        this.dragStartTime = Date.now();
        this.widget.classList.add('dragging');

        const rect = this.widget.getBoundingClientRect();
        this.dragOffset.x = e.clientX - rect.left;
        this.dragOffset.y = e.clientY - rect.top;

        // Convert to fixed positioning for smooth dragging
        this.widget.style.position = 'fixed';
        this.widget.style.left = rect.left + 'px';
        this.widget.style.top = rect.top + 'px';
        this.widget.style.right = 'auto';
        this.widget.style.bottom = 'auto';
        this.widget.style.zIndex = '10001';

        // Store current position
        this.currentPosition.left = rect.left;
        this.currentPosition.top = rect.top;
        this.currentPosition.right = null;
        this.currentPosition.bottom = null;

        // Add visual feedback
        this.widget.style.boxShadow = '0 10px 30px rgba(0,0,0,0.3)';
        this.widget.style.transform = 'scale(1.02)';
        this.widget.style.transition = 'none';

        console.log('✅ Drag started');
    }

    drag(e) {
        if (!this.isDragging) return;

        const newLeft = e.clientX - this.dragOffset.x;
        const newTop = e.clientY - this.dragOffset.y;

        // Keep widget within viewport with padding
        const padding = 10;
        const maxLeft = window.innerWidth - this.widget.offsetWidth - padding;
        const maxTop = window.innerHeight - this.widget.offsetHeight - padding;

        const constrainedLeft = Math.max(padding, Math.min(newLeft, maxLeft));
        const constrainedTop = Math.max(padding, Math.min(newTop, maxTop));

        this.widget.style.left = constrainedLeft + 'px';
        this.widget.style.top = constrainedTop + 'px';

        // Update position tracking
        this.currentPosition.left = constrainedLeft;
        this.currentPosition.top = constrainedTop;
    }

    stopDrag() {
        if (!this.isDragging) return;

        console.log('🎯 Stopping drag...');
        this.isDragging = false;
        this.widget.classList.remove('dragging');

        // Remove visual feedback
        this.widget.style.boxShadow = '';
        this.widget.style.transform = '';
        this.widget.style.transition = '';
        this.widget.style.zIndex = '10000';

        console.log('✅ Drag stopped');
    }

    startResize(e) {
        if (this.isMaximized) return; // Don't resize when maximized

        console.log('📏 Starting resize...');
        this.isResizing = true;
        this.resizeStartX = e.clientX;
        this.resizeStartY = e.clientY;
        this.resizeStartWidth = this.widget.offsetWidth;
        this.resizeStartHeight = this.widget.offsetHeight;

        // Add visual feedback
        this.widget.style.transition = 'none';
        this.widget.classList.add('resizing');

        console.log('✅ Resize started');
    }

    resize(e) {
        if (!this.isResizing) return;

        const deltaX = e.clientX - this.resizeStartX;
        const deltaY = e.clientY - this.resizeStartY;

        // Calculate new dimensions with constraints
        const minWidth = 300;
        const minHeight = 400;
        const maxWidth = window.innerWidth - 20;
        const maxHeight = window.innerHeight - 20;

        const newWidth = Math.max(minWidth, Math.min(maxWidth, this.resizeStartWidth + deltaX));
        const newHeight = Math.max(minHeight, Math.min(maxHeight, this.resizeStartHeight + deltaY));

        this.widget.style.width = newWidth + 'px';
        this.widget.style.height = newHeight + 'px';

        // Update stored dimensions
        this.originalPosition.width = newWidth + 'px';
        this.originalPosition.height = newHeight + 'px';
    }

    stopResize() {
        if (!this.isResizing) return;

        console.log('📏 Stopping resize...');
        this.isResizing = false;
        this.widget.classList.remove('resizing');
        this.widget.style.transition = '';

        console.log('✅ Resize stopped');
    }

    toggleMinimize() {
        if (this.animationInProgress) {
            console.log('⏳ Animation in progress, ignoring minimize toggle');
            return;
        }

        console.log('🔄 Toggling minimize state...');
        this.animationInProgress = true;

        if (this.isMinimized) {
            // Restore from minimized state
            this.restoreFromMinimized();
        } else {
            // Minimize the widget
            this.minimizeWidget();
        }

        // Reset animation flag after animation completes
        setTimeout(() => {
            this.animationInProgress = false;
            console.log('✅ Minimize animation completed');
        }, this.animationDuration + 50);
    }

    minimizeWidget() {
        console.log('➖ Minimizing widget...');

        // Save current state before minimizing
        this.stateBeforeMinimize.wasExpanded = this.isExpanded;
        this.stateBeforeMinimize.wasCollapsed = !this.isExpanded && !this.isMinimized;

        // Preserve scroll position
        this.preserveScrollPosition();

        // Hide backdrop if expanded
        if (this.isExpanded) {
            this.backdrop.classList.remove('active');
        }

        // Add minimized class
        this.widget.classList.add('minimized');

        // Update state flags
        this.isMinimized = true;
        this.isExpanded = false;

        // Update button icon to indicate restore functionality
        const minimizeBtn = this.widget.querySelector('.minimize-btn i');
        if (minimizeBtn) {
            minimizeBtn.className = 'fas fa-window-restore';
            minimizeBtn.parentElement.title = 'Restore';
        }

        console.log('✅ Widget minimized to header-only view');
    }

    restoreFromMinimized() {
        console.log('📂 Restoring from minimized state...');

        // Remove minimized class
        this.widget.classList.remove('minimized');

        // Restore to previous state
        if (this.stateBeforeMinimize.wasExpanded) {
            // Restore to expanded state
            this.widget.classList.add('chat-expanded');
            this.widget.classList.remove('chat-collapsed');
            this.backdrop.classList.add('active');
            this.isExpanded = true;

            // Update expand button icon
            const maximizeBtn = this.widget.querySelector('.maximize-btn i');
            if (maximizeBtn) {
                maximizeBtn.className = 'fas fa-compress';
            }
        } else {
            // Restore to collapsed state
            this.widget.classList.add('chat-collapsed');
            this.widget.classList.remove('chat-expanded');
            this.isExpanded = false;

            // Update expand button icon
            const maximizeBtn = this.widget.querySelector('.maximize-btn i');
            if (maximizeBtn) {
                maximizeBtn.className = 'fas fa-expand';
            }
        }

        // Update state flags
        this.isMinimized = false;

        // Update minimize button icon back to minimize
        const minimizeBtn = this.widget.querySelector('.minimize-btn i');
        if (minimizeBtn) {
            minimizeBtn.className = 'fas fa-minus';
            minimizeBtn.parentElement.title = 'Minimize';
        }

        // Restore scroll position
        setTimeout(() => {
            this.restoreScrollPosition();
        }, 50);

        console.log('✅ Widget restored from minimized state');
    }

    toggleMaximize() {
        console.log('🔍 Toggling maximize...');

        if (this.isMaximized) {
            // Restore to original size and position
            console.log('📉 Restoring from maximized...');
            this.widget.classList.remove('maximized');

            // Restore size
            this.widget.style.width = this.originalPosition.width;
            this.widget.style.height = this.originalPosition.height;

            // Restore position
            if (this.currentPosition.left !== null && this.currentPosition.top !== null) {
                this.widget.style.left = this.currentPosition.left + 'px';
                this.widget.style.top = this.currentPosition.top + 'px';
                this.widget.style.right = 'auto';
                this.widget.style.bottom = 'auto';
            } else {
                this.widget.style.right = this.originalPosition.right;
                this.widget.style.bottom = this.originalPosition.bottom;
                this.widget.style.left = 'auto';
                this.widget.style.top = 'auto';
            }

            // Update button icon
            const maximizeBtn = this.widget.querySelector('.maximize-btn i');
            if (maximizeBtn) {
                maximizeBtn.className = 'fas fa-expand';
            }

            this.isMaximized = false;
            console.log('✅ Restored from maximized');
        } else {
            // Save current position and size
            console.log('📈 Maximizing...');
            const rect = this.widget.getBoundingClientRect();

            this.originalPosition.width = this.widget.style.width || rect.width + 'px';
            this.originalPosition.height = this.widget.style.height || rect.height + 'px';

            if (this.widget.style.left && this.widget.style.top) {
                this.currentPosition.left = parseInt(this.widget.style.left);
                this.currentPosition.top = parseInt(this.widget.style.top);
            }

            // Maximize to full screen with padding
            this.widget.classList.add('maximized');
            this.widget.style.position = 'fixed';
            this.widget.style.top = '10px';
            this.widget.style.left = '10px';
            this.widget.style.right = '10px';
            this.widget.style.bottom = '10px';
            this.widget.style.width = 'auto';
            this.widget.style.height = 'auto';

            // Update button icon
            const maximizeBtn = this.widget.querySelector('.maximize-btn i');
            if (maximizeBtn) {
                maximizeBtn.className = 'fas fa-compress';
            }

            this.isMaximized = true;
            console.log('✅ Maximized');
        }
    }
}

// Debug function to check chat widget status
function debugChatWidget() {
    console.log('🔍 Chat Widget Debug Info:');
    console.log('- Widget element:', document.querySelector('.chat-widget'));
    console.log('- Container element:', document.querySelector('.chat-container'));
    console.log('- Messages element:', document.querySelector('.chat-messages'));
    console.log('- Toggle element:', document.querySelector('.chat-toggle'));
    console.log('- Input element:', document.querySelector('.message-input'));
    console.log('- Send button:', document.querySelector('.send-button'));
    console.log('- Suggestion chips:', document.querySelector('.suggestion-chips'));
    console.log('- Chat widget instance:', window.chatWidget);
}

// Force chat widget initialization
function initializeChatWidget() {
    console.log('🚀 Force initializing chat widget...');

    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                window.chatWidget = new ChatWidget();
                debugChatWidget();
            }, 200);
        });
    } else {
        // DOM is already ready
        setTimeout(() => {
            window.chatWidget = new ChatWidget();
            debugChatWidget();
        }, 200);
    }
}

// Initialize chat widget
initializeChatWidget();

// Also initialize on window load as backup
window.addEventListener('load', () => {
    if (!window.chatWidget) {
        console.log('🔄 Backup initialization...');
        window.chatWidget = new ChatWidget();
        debugChatWidget();
    }
});

// Manual test function - can be called from browser console
window.testChatWidget = function() {
    console.log('🧪 Manual Chat Widget Test...');

    // Force create a new chat widget
    window.chatWidget = new ChatWidget();

    // Force open the chat widget with proper expansion
    const widget = document.querySelector('.chat-widget');
    if (widget) {
        widget.classList.remove('minimized');

        // Force show content
        const chatContent = widget.querySelector('.chat-content');
        if (chatContent) {
            chatContent.style.display = 'flex';
            chatContent.style.opacity = '1';
            chatContent.style.transform = 'translateY(0)';
            chatContent.style.height = 'calc(100% - 60px)';
        }

        console.log('✅ Chat widget opened and expanded');
    }

    // Test adding a message manually
    setTimeout(() => {
        if (window.chatWidget && window.chatWidget.messagesContainer) {
            const testMessage = document.createElement('div');
            testMessage.className = 'message bot-message';
            testMessage.style.cssText = `
                margin-bottom: 1rem;
                max-width: 80%;
                padding: 0.8rem 1rem;
                border-radius: 15px 15px 15px 0;
                background: #f5f5f5;
                margin-right: auto;
                opacity: 1;
                transform: translateY(0);
            `;
            testMessage.innerHTML = "🧪 Test message - Chat widget expansion is working!";
            window.chatWidget.messagesContainer.appendChild(testMessage);
            console.log('✅ Test message added');
        }
    }, 500);

    console.log('🎯 Test complete. Check the chat widget!');
};

// Quick expand function
window.expandChatWidget = function() {
    const widget = document.querySelector('.chat-widget');
    if (widget) {
        widget.classList.remove('minimized');
        const chatContent = widget.querySelector('.chat-content');
        if (chatContent) {
            chatContent.style.display = 'flex';
            chatContent.style.opacity = '1';
            chatContent.style.transform = 'translateY(0)';
        }
        console.log('✅ Chat widget force expanded');
    }
};

// Quick fix function for blank content
window.fixBlankChat = function() {
    console.log('🔧 Fixing blank chat content...');

    const widget = document.querySelector('.chat-widget');
    if (widget && window.chatWidget) {
        // Remove minimized class
        widget.classList.remove('minimized');
        window.chatWidget.isMinimized = false;

        // Force content visibility
        if (window.chatWidget.forceContentVisibility) {
            window.chatWidget.forceContentVisibility();
        }

        // Force initialize content
        if (window.chatWidget.forceInitializeContent) {
            window.chatWidget.forceInitializeContent();
        }

        console.log('✅ Blank chat fix applied');
    } else {
        console.error('❌ Chat widget not found');
    }
};

// Test minimize functionality
window.testMinimize = function() {
    console.log('🧪 Testing minimize functionality...');

    if (window.chatWidget) {
        console.log('Current state:', {
            isMinimized: window.chatWidget.isMinimized,
            isExpanded: window.chatWidget.isExpanded,
            classes: window.chatWidget.widget.className
        });

        // Test minimize toggle
        window.chatWidget.toggleMinimize();

        setTimeout(() => {
            console.log('After minimize toggle:', {
                isMinimized: window.chatWidget.isMinimized,
                isExpanded: window.chatWidget.isExpanded,
                classes: window.chatWidget.widget.className
            });
        }, 500);
    } else {
        console.error('❌ Chat widget not found');
    }
};

// Drag functionality
let isDragging = false;
let currentX;
let currentY;
let initialX;
let initialY;
let xOffset = 0;
let yOffset = 0;

function initDragFunctionality() {
    const chatWidget = document.getElementById('chatWidget');
    const dragHandle = chatWidget.querySelector('.chat-toggle');

    dragHandle.addEventListener('mousedown', dragStart);
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', dragEnd);
    
    // Touch events support
    dragHandle.addEventListener('touchstart', dragStart);
    document.addEventListener('touchmove', drag);
    document.addEventListener('touchend', dragEnd);

    function dragStart(e) {
        if (chatWidget.classList.contains('chat-expanded')) return;
        
        const evt = e.type === 'touchstart' ? e.touches[0] : e;
        initialX = evt.clientX - xOffset;
        initialY = evt.clientY - yOffset;

        if (e.target === dragHandle) {
            isDragging = true;
            chatWidget.classList.add('dragging');
        }
    }

    function drag(e) {
        if (!isDragging) return;
        e.preventDefault();

        const evt = e.type === 'touchmove' ? e.touches[0] : e;
        currentX = evt.clientX - initialX;
        currentY = evt.clientY - initialY;

        xOffset = currentX;
        yOffset = currentY;

        // Ensure the widget stays within viewport bounds
        const rect = chatWidget.getBoundingClientRect();
        const maxX = window.innerWidth - rect.width;
        const maxY = window.innerHeight - rect.height;

        currentX = Math.min(Math.max(currentX, 0), maxX);
        currentY = Math.min(Math.max(currentY, 0), maxY);

        setTranslate(currentX, currentY, chatWidget);
    }

    function dragEnd() {
        if (!isDragging) return;
        
        initialX = currentX;
        initialY = currentY;
        isDragging = false;

        chatWidget.classList.remove('dragging');
    }

    function setTranslate(xPos, yPos, el) {
        el.style.transform = `translate3d(${xPos}px, ${yPos}px, 0)`;
    }
}

// Initialize drag functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initDragFunctionality();
    // ...existing code...
});