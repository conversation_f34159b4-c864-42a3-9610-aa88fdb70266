// Navigation functionality
document.addEventListener('DOMContentLoaded', () => {
    // Get current page path
    const currentPath = window.location.pathname;
    const currentPage = currentPath === '/' ? 'home' : currentPath.substring(1);

    // Find all nav links
    const navLinks = document.querySelectorAll('.nav-link');

    // Function to update active state
    const updateActiveState = () => {
        const currentPath = window.location.pathname;
        const currentPage = currentPath === '/' ? 'home' : currentPath.substring(1);
        
        // Remove active class from all links
        navLinks.forEach(link => {
            link.classList.remove('active');
        });

        // Add active class to current page link
        const activeLink = document.querySelector(`.nav-link[data-page="${currentPage}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    };

    // Initial active state
    updateActiveState();

    // Add click handlers for navigation
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault(); // Prevent default link behavior
            
            // Get the href from the clicked link
            const href = link.getAttribute('href');
            
            // Update the URL without reloading the page
            window.history.pushState({}, '', href);
            
            // Update active state
            updateActiveState();
            
            // Navigate to the page
            window.location.href = href;
        });
    });

    // Handle browser back/forward buttons
    window.addEventListener('popstate', () => {
        updateActiveState();
    });
}); 