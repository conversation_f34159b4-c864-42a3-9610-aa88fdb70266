const { OrdersService } = require('../services/ordersService');

async function placeOrder(userId, items, totalPrice) {
    try {
        const ordersService = new OrdersService(userId);
        const order = await ordersService.placeOrder(items, totalPrice);
        return {
            success: true,
            orderId: order.id,
            message: 'Order placed successfully',
            estimatedDelivery: '30-45 minutes'
        };
    } catch (error) {
        console.error('Error placing order:', error);
        throw error;
    }
}

module.exports = { placeOrder };
