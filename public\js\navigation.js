// Navigation System for Torbaaz Food Delivery
class NavigationManager {
    constructor() {
        this.currentPage = this.getCurrentPage();
        this.cartCount = 0;
        this.init();
    }

    init() {
        this.updateActiveNavigation();
        this.setupEventListeners();
        this.updateCartBadge();
        this.setupMobileMenu();
        console.log('🧭 Navigation system initialized');
    }

    getCurrentPage() {
        const path = window.location.pathname;
        if (path === '/' || path === '/index.html' || path.endsWith('index.html')) return 'home';
        if (path.includes('restaurants')) return 'restaurants';
        if (path.includes('deals')) return 'deals';
        if (path.includes('orders')) return 'orders';
        if (path.includes('about')) return 'about';
        if (path.includes('cart')) return 'cart';
        return 'home';
    }

    updateActiveNavigation() {
        // Remove active class from all nav links
        document.querySelectorAll('.nav-links a, .nav-links li a, .nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // Add active class to current page link
        const selectors = [
            `[href="/${this.currentPage}.html"]`,
            `[href="${this.currentPage}.html"]`,
            `[href="/${this.currentPage}"]`,
            `[href="${this.currentPage}"]`
        ];

        if (this.currentPage === 'home') {
            selectors.push('[href="/"]', '[href="index.html"]', '[href="/index.html"]');
        }

        selectors.forEach(selector => {
            const links = document.querySelectorAll(selector);
            links.forEach(link => link.classList.add('active'));
        });
    }

    setupEventListeners() {
        // Cart button functionality
        document.querySelectorAll('.cart-btn, .cart-icon').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.navigateToCart();
            });
        });

        // Navigation links
        document.querySelectorAll('.nav-links a, .nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                if (href && !href.startsWith('#')) {
                    // Allow normal navigation
                    setTimeout(() => this.updateActiveNavigation(), 100);
                }
            });
        });

        // Logo click
        document.querySelectorAll('.logo a, .logo').forEach(logo => {
            logo.addEventListener('click', (e) => {
                if (!logo.getAttribute('href')) {
                    e.preventDefault();
                    this.navigateToHome();
                }
            });
        });
    }

    navigateToHome() {
        window.location.href = '/index.html';
    }

    navigateToRestaurants() {
        window.location.href = '/restaurants.html';
    }

    navigateToDeals() {
        window.location.href = '/deals.html';
    }

    navigateToOrders() {
        window.location.href = '/orders.html';
    }

    navigateToAbout() {
        window.location.href = '/about.html';
    }

    navigateToCart() {
        window.location.href = '/cart.html';
    }

    updateCartBadge() {
        const cart = this.getCartFromStorage();
        this.cartCount = cart.reduce((total, item) => total + item.quantity, 0);

        const badges = document.querySelectorAll('.cart-badge');
        badges.forEach(badge => {
            badge.textContent = this.cartCount;
            badge.style.display = this.cartCount > 0 ? 'block' : 'none';
        });
    }

    getCartFromStorage() {
        try {
            return JSON.parse(localStorage.getItem('torbaaz_cart')) || [];
        } catch (error) {
            console.error('Error reading cart from storage:', error);
            return [];
        }
    }

    // Mobile menu functionality
    setupMobileMenu() {
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const mobileMenu = document.querySelector('.mobile-menu');

        if (mobileMenuBtn && mobileMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('active');
                mobileMenuBtn.classList.toggle('active');
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!mobileMenu.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                    mobileMenu.classList.remove('active');
                    mobileMenuBtn.classList.remove('active');
                }
            });
        }
    }
}

// Global navigation functions
function viewCart() {
    window.location.href = '/cart.html';
}

function goToHome() {
    window.location.href = '/index.html';
}

function goToRestaurants() {
    window.location.href = '/restaurants.html';
}

function goToDeals() {
    window.location.href = '/deals.html';
}

function goToOrders() {
    window.location.href = '/orders.html';
}

function goToAbout() {
    window.location.href = '/about.html';
}

// Initialize navigation when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.navigationManager = new NavigationManager();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NavigationManager;
}