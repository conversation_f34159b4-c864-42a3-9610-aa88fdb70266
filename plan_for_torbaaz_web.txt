# Torbaaz Web App Migration & AI Integration Plan
http://localhost:3000
## Phase 0: Prepare Your ChatGPT Assistant (Playground)

1. Create a Chat Session
   - In the OpenAI Playground, choose the Assistants API template.
   - Paste your System message (the "Jarvis" spec) into the System pane.
   - In the Functions pane, paste the JSON from openai_functions.json.
2. Save & Note Your Assistant ID
   - Click Save and copy the Assistant ID (e.g., asst_uReDmCJ2C9fRZll33zD2Tb0K).

---

## Phase 1: Project Bootstrap

1. Use already made Flutter project in current directory.
2. Create New Node.js Project:
   ```bash
   mkdir torbaaz-web && cd torbaaz-web
   npm init -y
   npm install express dotenv openai
   npm install --save-dev nodemon
   ```
3. Set Up Directory Structure:
   ```
   torbaaz-web/
   ├── src/
   │   ├── app.js
   │   ├── server.js
   │   ├── config/
   │   ├── routes/
   │   ├── controllers/
   │   ├── services/
   │   ├── tools/
   │   └── utils/
   ├── public/           # your converted Flutter web assets (HTML/CSS/JS)
   ├── .env
   ├── .gitignore
   ├── package.json
   └── README.md
   ```
4. Populate `.env`:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   OPENAI_ASSISTANT_ID=your_assistant_id_here
   PORT=3000
   ```

---

## Phase 2: Backend Core

1. OpenAI Client & Assistant Service
   - In `src/config/openai.js`, initialize the OpenAI client.
   - In `src/services/assistantService.js`, write `queryJarvis(userId, message)` that:
     1. Calls chat.completions.create with assistant_id (from `.env`).
     2. Handles function_call → invokes your local tools[name].
     3. Feeds the result back for a final reply.
2. Custom Tools
   - Copy your Flutter data (JSON/CSV) into `src/tools/` as JS modules:
     - `getRestaurantInfo.js`
     - `getMenu.js`
     - `getFoodDetails.js`
     - `manageFavorites.js`
     - `placeOrder.js`
     - `answerGeneralQuery.js`
   - Each exports an async function matching its function schema.
3. Routes & Controllers
   - In `src/routes/assistantRoutes.js`, define:
     ```js
     router.post('/chat', assistantController.handleChat);
     ```
   - In `src/controllers/assistantController.js`, call `queryJarvis(req.body.userId, req.body.text)` and send JSON.

---

## Phase 3: Frontend Integration

1. Convert Flutter Web Build
   - From `torbaaz-flutter/build/web`, copy `index.html`, `main.dart.js`, and assets into `public/`.
2. Chat UI
   - Add a chat widget in `public/index.html` that POSTs to `/api/chat`.
   - Display Jarvis's responses and handle user input.

---

## Phase 4: Testing & Validation

1. Local Testing
   - Start the server:
     ```bash
     npm run dev
     ```
   - Use Postman or curl:
``` 