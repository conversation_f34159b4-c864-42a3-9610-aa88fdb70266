<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Exclusive deals and offers from top restaurants">
    <title>Exclusive Deals - Torbaaz Food Delivery</title>
    
    <!-- Styles -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#ff6b35',
                        background: '#FFF5F0',
                    },
                    fontFamily: {
                        poppins: ['Poppins', 'sans-serif'],
                    },
                }
            }
        }
    </script>
</head>
<body class="bg-background font-poppins">
    <!-- Header -->
    <header class="bg-white shadow-md sticky top-0 z-50">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <a href="/" class="flex items-center gap-2">
                        <img src="/assets/images/logo.png" alt="Torbaaz" class="h-10 w-auto">
                        <span class="text-2xl font-bold text-primary">Torbaaz</span>
                    </a>
                </div>
                
                <div class="hidden md:flex items-center space-x-8">
                    <a href="/" class="text-gray-600 hover:text-primary transition-colors">Home</a>
                    <a href="/menu" class="text-gray-600 hover:text-primary transition-colors">Menu</a>
                    <a href="/deals" class="text-primary font-medium">Deals</a>
                    <a href="/contact" class="text-gray-600 hover:text-primary transition-colors">Contact</a>
                </div>

                <div class="flex items-center gap-4">
                    <div class="relative">
                        <button class="p-2 hover:bg-gray-100 rounded-full">
                            <i class="fas fa-shopping-cart text-gray-600"></i>
                            <span class="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                        </button>
                    </div>
                    <img src="/assets/images/avatar.png" alt="Profile" class="h-10 w-10 rounded-full">
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 class="text-3xl font-bold mb-8">Exclusive Deals</h1>

        <!-- Deal Categories -->
        <div class="flex border-b border-gray-200 mb-8">
            <button class="text-primary border-b-2 border-primary px-4 py-2 font-medium">All Deals</button>
            <button class="text-gray-600 hover:text-primary px-4 py-2">Lunch Specials</button>
            <button class="text-gray-600 hover:text-primary px-4 py-2">Dinner Deals</button>
            <button class="text-gray-600 hover:text-primary px-4 py-2">Family Packs</button>
        </div>

        <!-- Deals Grid -->
        <div class="space-y-6">
            <!-- Deal Card 1 -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="md:flex">
                    <div class="md:w-1/3">
                        <img src="/assets/images/family-feast.jpg" alt="Family Feast" class="h-full w-full object-cover">
                    </div>
                    <div class="p-6 md:w-2/3">
                        <div class="flex justify-between items-start">
                            <div>
                                <h2 class="text-xl font-semibold mb-2">Family Feast</h2>
                                <p class="text-gray-600 mb-4">Enjoy a complete meal for the whole family with our special pack.</p>
                                <div class="flex items-center gap-4 mb-4">
                                    <span class="text-2xl font-bold text-primary">$49.99</span>
                                    <span class="text-gray-500 line-through">$65.00</span>
                                    <span class="bg-red-100 text-red-600 px-2 py-1 rounded-full text-sm font-medium">Save 23%</span>
                                </div>
                            </div>
                            <button class="bg-primary text-white px-6 py-2 rounded-full hover:bg-primary/90 transition-colors">
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Deal Card 2 -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="md:flex">
                    <div class="md:w-1/3">
                        <img src="/assets/images/lunch-special.jpg" alt="Lunchtime Delight" class="h-full w-full object-cover">
                    </div>
                    <div class="p-6 md:w-2/3">
                        <div class="flex justify-between items-start">
                            <div>
                                <h2 class="text-xl font-semibold mb-2">Lunchtime Delight</h2>
                                <p class="text-gray-600 mb-4">Quick and delicious lunch options to power through your day.</p>
                                <div class="flex items-center gap-4 mb-4">
                                    <span class="text-2xl font-bold text-primary">$12.99</span>
                                    <span class="text-gray-500 line-through">$18.00</span>
                                    <span class="bg-red-100 text-red-600 px-2 py-1 rounded-full text-sm font-medium">Save 28%</span>
                                </div>
                            </div>
                            <button class="bg-primary text-white px-6 py-2 rounded-full hover:bg-primary/90 transition-colors">
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Deal Card 3 -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="md:flex">
                    <div class="md:w-1/3">
                        <img src="/assets/images/dinner-combo.jpg" alt="Dinner Combo" class="h-full w-full object-cover">
                    </div>
                    <div class="p-6 md:w-2/3">
                        <div class="flex justify-between items-start">
                            <div>
                                <h2 class="text-xl font-semibold mb-2">Dinner Combo</h2>
                                <p class="text-gray-600 mb-4">A perfect dinner combination to end your day on a delicious note.</p>
                                <div class="flex items-center gap-4 mb-4">
                                    <span class="text-2xl font-bold text-primary">$29.99</span>
                                    <span class="text-gray-500 line-through">$40.00</span>
                                    <span class="bg-red-100 text-red-600 px-2 py-1 rounded-full text-sm font-medium">Save 25%</span>
                                </div>
                            </div>
                            <button class="bg-primary text-white px-6 py-2 rounded-full hover:bg-primary/90 transition-colors">
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white mt-16 border-t border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">About Us</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-600 hover:text-primary">Our Story</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary">Blog</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary">Careers</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">For Restaurants</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-600 hover:text-primary">Partner with us</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary">Restaurant app</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary">Business Blog</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Support</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-600 hover:text-primary">Help Center</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary">Contact Us</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary">Privacy Policy</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Follow Us</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-600 hover:text-primary"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-600 hover:text-primary"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-600 hover:text-primary"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            <div class="mt-8 pt-8 border-t border-gray-200 text-center">
                <p class="text-gray-500">&copy; 2024 Torbaaz Food Delivery. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/main.js"></script>
    <script src="/js/navigation.js"></script>
</body>
</html>
