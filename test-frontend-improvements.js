const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
const fs = require('fs');
const path = require('path');

async function testFrontendImprovements() {
    console.log('🚀 Testing All Frontend Improvements for Torbaaz...\n');
    
    let passedTests = 0;
    let totalTests = 0;

    // Test 1: AI Chat Widget Enhancements
    totalTests++;
    console.log('1. 🤖 Testing Enhanced AI Chat Widget...');
    try {
        const chatCssPath = path.join(__dirname, 'public', 'css', 'chat.css');
        const chatJsPath = path.join(__dirname, 'public', 'js', 'chat.js');
        
        if (fs.existsSync(chatCssPath) && fs.existsSync(chatJsPath)) {
            const chatCss = fs.readFileSync(chatCssPath, 'utf8');
            const chatJs = fs.readFileSync(chatJsPath, 'utf8');
            
            const hasResizeFeatures = chatCss.includes('resize: both') && 
                                    chatCss.includes('dragging') &&
                                    chatCss.includes('maximized');
            
            const hasDragFeatures = chatJs.includes('setupDragAndResize') &&
                                  chatJs.includes('startDrag') &&
                                  chatJs.includes('toggleMaximize');
            
            if (hasResizeFeatures && hasDragFeatures) {
                console.log('   ✅ Chat Widget Enhanced: Resizable, draggable, and maximizable');
                console.log('   📝 Features: Drag handles, resize controls, maximize/minimize buttons');
                passedTests++;
            } else {
                console.log('   ❌ Chat Widget: Enhancement features missing');
            }
        } else {
            console.log('   ❌ Chat Widget: Files not found');
        }
    } catch (error) {
        console.log('   ❌ Chat Widget: Error checking files');
    }

    // Test 2: Missing Pages Development
    totalTests++;
    console.log('\n2. 📄 Testing Missing Pages Development...');
    try {
        const dealsPagePath = path.join(__dirname, 'public', 'deals.html');
        const cartPagePath = path.join(__dirname, 'public', 'cart.html');
        const dealsJsPath = path.join(__dirname, 'public', 'js', 'deals.js');
        const cartJsPath = path.join(__dirname, 'public', 'js', 'cart.js');
        
        const pagesExist = fs.existsSync(dealsPagePath) && 
                          fs.existsSync(cartPagePath) &&
                          fs.existsSync(dealsJsPath) &&
                          fs.existsSync(cartJsPath);
        
        if (pagesExist) {
            const dealsContent = fs.readFileSync(dealsPagePath, 'utf8');
            const cartContent = fs.readFileSync(cartPagePath, 'utf8');
            
            const dealsHasFeatures = dealsContent.includes('deals-filters') &&
                                   dealsContent.includes('deals-grid');
            
            const cartHasFeatures = cartContent.includes('cart-items-section') &&
                                  cartContent.includes('order-summary');
            
            if (dealsHasFeatures && cartHasFeatures) {
                console.log('   ✅ Missing Pages: Created with full functionality');
                console.log('   📝 Pages: Deals page with filters, Cart page with checkout');
                passedTests++;
            } else {
                console.log('   ❌ Missing Pages: Content incomplete');
            }
        } else {
            console.log('   ❌ Missing Pages: Files not created');
        }
    } catch (error) {
        console.log('   ❌ Missing Pages: Error checking files');
    }

    // Test 3: Logo Enhancement
    totalTests++;
    console.log('\n3. 🎨 Testing Logo Enhancement...');
    try {
        const cssPath = path.join(__dirname, 'public', 'css', 'main.css');
        
        if (fs.existsSync(cssPath)) {
            const cssContent = fs.readFileSync(cssPath, 'utf8');
            
            const hasEnhancedLogo = cssContent.includes('height: 80px') &&
                                  cssContent.includes('font-size: 2.5rem') &&
                                  cssContent.includes('transform: scale(1.05)');
            
            if (hasEnhancedLogo) {
                console.log('   ✅ Logo Enhanced: Increased size with hover effects');
                console.log('   📝 Features: 80px height, 2.5rem font, hover animations');
                passedTests++;
            } else {
                console.log('   ❌ Logo Enhancement: Size not increased properly');
            }
        } else {
            console.log('   ❌ Logo Enhancement: CSS file not found');
        }
    } catch (error) {
        console.log('   ❌ Logo Enhancement: Error checking CSS');
    }

    // Test 4: Search Bar Functionality
    totalTests++;
    console.log('\n4. 🔍 Testing Advanced Search Functionality...');
    try {
        const searchJsPath = path.join(__dirname, 'public', 'js', 'search.js');
        const cssPath = path.join(__dirname, 'public', 'css', 'main.css');
        
        if (fs.existsSync(searchJsPath) && fs.existsSync(cssPath)) {
            const searchJs = fs.readFileSync(searchJsPath, 'utf8');
            const cssContent = fs.readFileSync(cssPath, 'utf8');
            
            const hasSearchFeatures = searchJs.includes('SearchEngine') &&
                                    searchJs.includes('showSuggestions') &&
                                    searchJs.includes('performSearch') &&
                                    searchJs.includes('loadSearchData');
            
            const hasSearchCSS = cssContent.includes('search-suggestions') &&
                                cssContent.includes('search-results') &&
                                cssContent.includes('suggestion-item');
            
            if (hasSearchFeatures && hasSearchCSS) {
                console.log('   ✅ Search Functionality: Comprehensive search implemented');
                console.log('   📝 Features: Autocomplete, suggestions, intelligent results');
                passedTests++;
            } else {
                console.log('   ❌ Search Functionality: Implementation incomplete');
            }
        } else {
            console.log('   ❌ Search Functionality: Files not found');
        }
    } catch (error) {
        console.log('   ❌ Search Functionality: Error checking files');
    }

    // Test 5: API Integration
    totalTests++;
    console.log('\n5. 🔌 Testing API Integration...');
    try {
        // Test chat endpoint
        const chatResponse = await fetch('http://localhost:3000/api/chat/chat', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                userId: 'test_improvements',
                text: 'Test search functionality'
            })
        });

        // Test deals endpoint
        const dealsResponse = await fetch('http://localhost:3000/api/restaurants/deals');
        
        if (chatResponse.ok && dealsResponse.ok) {
            const chatData = await chatResponse.json();
            const dealsData = await dealsResponse.json();
            
            if (chatData.reply && Array.isArray(dealsData)) {
                console.log('   ✅ API Integration: All endpoints working');
                console.log('   📝 Endpoints: Chat, deals, restaurants all functional');
                passedTests++;
            } else {
                console.log('   ❌ API Integration: Response format issues');
            }
        } else {
            console.log('   ❌ API Integration: HTTP errors');
        }
    } catch (error) {
        console.log('   ❌ API Integration: Connection failed');
    }

    // Test 6: CSS Enhancements
    totalTests++;
    console.log('\n6. 🎨 Testing CSS Enhancements...');
    try {
        const cssPath = path.join(__dirname, 'public', 'css', 'main.css');
        
        if (fs.existsSync(cssPath)) {
            const cssContent = fs.readFileSync(cssPath, 'utf8');
            
            const hasDealsCSS = cssContent.includes('deals-hero') &&
                              cssContent.includes('deal-card') &&
                              cssContent.includes('discount-badge');
            
            const hasSearchCSS = cssContent.includes('search-suggestions') &&
                               cssContent.includes('search-results');
            
            const hasResponsiveCSS = cssContent.includes('@media (max-width: 768px)');
            
            if (hasDealsCSS && hasSearchCSS && hasResponsiveCSS) {
                console.log('   ✅ CSS Enhancements: Comprehensive styling added');
                console.log('   📝 Features: Deals styling, search UI, responsive design');
                passedTests++;
            } else {
                console.log('   ❌ CSS Enhancements: Some styles missing');
            }
        } else {
            console.log('   ❌ CSS Enhancements: CSS file not found');
        }
    } catch (error) {
        console.log('   ❌ CSS Enhancements: Error checking CSS');
    }

    // Test 7: JavaScript Functionality
    totalTests++;
    console.log('\n7. ⚡ Testing JavaScript Functionality...');
    try {
        const jsFiles = [
            'public/js/search.js',
            'public/js/deals.js',
            'public/js/cart.js',
            'public/js/chat.js'
        ];
        
        let allFilesExist = true;
        let hasRequiredFunctions = true;
        
        jsFiles.forEach(file => {
            const filePath = path.join(__dirname, file);
            if (!fs.existsSync(filePath)) {
                allFilesExist = false;
                return;
            }
            
            const content = fs.readFileSync(filePath, 'utf8');
            
            // Check for key functions based on file
            if (file.includes('search.js') && !content.includes('SearchEngine')) {
                hasRequiredFunctions = false;
            }
            if (file.includes('deals.js') && !content.includes('loadDeals')) {
                hasRequiredFunctions = false;
            }
            if (file.includes('cart.js') && !content.includes('CartManager')) {
                hasRequiredFunctions = false;
            }
            if (file.includes('chat.js') && !content.includes('setupDragAndResize')) {
                hasRequiredFunctions = false;
            }
        });
        
        if (allFilesExist && hasRequiredFunctions) {
            console.log('   ✅ JavaScript Functionality: All modules implemented');
            console.log('   📝 Modules: Search engine, deals manager, cart system, enhanced chat');
            passedTests++;
        } else {
            console.log('   ❌ JavaScript Functionality: Some modules missing or incomplete');
        }
    } catch (error) {
        console.log('   ❌ JavaScript Functionality: Error checking files');
    }

    // Final Summary
    console.log('\n' + '='.repeat(70));
    console.log('🎯 TORBAAZ FRONTEND IMPROVEMENTS SUMMARY');
    console.log('='.repeat(70));
    console.log(`✅ Tests Passed: ${passedTests}/${totalTests}`);
    console.log(`📊 Success Rate: ${Math.round((passedTests/totalTests) * 100)}%`);
    
    if (passedTests === totalTests) {
        console.log('\n🎉 ALL FRONTEND IMPROVEMENTS SUCCESSFULLY IMPLEMENTED!');
        console.log('🚀 Torbaaz Food Delivery Platform Enhanced Features:');
        console.log('   ✅ Resizable & Draggable AI Chat Widget');
        console.log('   ✅ Comprehensive Deals Page with Filtering');
        console.log('   ✅ Full-Featured Shopping Cart Page');
        console.log('   ✅ Enhanced Logo with Hover Effects');
        console.log('   ✅ Advanced Search with Autocomplete');
        console.log('   ✅ Responsive Design for All Devices');
        console.log('   ✅ Complete API Integration');
        console.log('\n💡 The website now offers a premium user experience!');
        console.log('🌟 Ready for production deployment with all requested features!');
    } else {
        console.log('\n⚠️  Some improvements need attention. Check the failed tests above.');
    }
}

testFrontendImprovements();
