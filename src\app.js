const express = require('express');
const path = require('path');
const dotenv = require('dotenv');
const restaurantRoutes = require('./routes/restaurantRoutes');

// Load environment variables
dotenv.config();

const app = express();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Enable CORS
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
    next();
});

// Serve static files from public directory (Flutter web build)
app.use(express.static(path.join(__dirname, '../public')));

// API Routes
app.use('/api/chat', require('./routes/chatRoutes'));
app.use('/api/restaurants', restaurantRoutes);
app.use('/api/orders', require('./routes/ordersRoutes'));
app.use('/api/favorites', require('./routes/favoritesRoutes'));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

module.exports = app; 