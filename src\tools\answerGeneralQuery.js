const dataset = require('../data/dataset');

/**
 * Answer general queries about restaurants and menu items
 * @param {string} query
 * @returns {array}
 */
const answerGeneralQuery = async (query) => {
  const q = query.toLowerCase();
  const results = [];
  for (const restaurant of dataset.restaurants) {
    if (restaurant.name.toLowerCase().includes(q)) {
      results.push({ type: 'restaurant', ...restaurant });
    }
    for (const item of restaurant.menu) {
      if (item.name.toLowerCase().includes(q) || (item.description && item.description.toLowerCase().includes(q))) {
        results.push({ type: 'menu', restaurant: restaurant.name, ...item });
      }
    }
  }
  return results;
};

module.exports = answerGeneralQuery;
