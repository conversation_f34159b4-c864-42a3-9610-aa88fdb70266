const OpenAI = require('openai');
const restaurantService = require('./restaurantService');
const fs = require('fs');
const path = require('path');

// Initialize OpenAI client
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY
});

// Store active threads
const activeThreads = new Map();

// Load restaurant data for context
function loadRestaurantData() {
    try {
        const dataPath = path.join(__dirname, '../../data/ai_assistant_data.txt');
        const data = fs.readFileSync(dataPath, 'utf8');
        return data;
    } catch (error) {
        console.error('Error loading restaurant data:', error);
        return '';
    }
}

/**
 * Query the Jarvis assistant with restaurant context
 * @param {string} userId
 * @param {string} message
 * @returns {Promise<string>}
 */

async function queryJarvis(userId, message) {
    try {
        // Get conversation history
        const conversationHistory = activeThreads.get(userId) || [];

        // Load restaurant data for context
        const restaurantData = loadRestaurantData();

        // Get current restaurants from database
        let restaurantsInfo = '';
        try {
            const restaurants = await restaurantService.getAllRestaurants();
            restaurantsInfo = restaurants.map(r =>
                `${r.name}: ${r.description}, Rating: ${r.rating}, Delivery: ${r.delivery_time}, Fee: Rs.${r.delivery_fee}`
            ).join('\n');
        } catch (error) {
            console.error('Error fetching restaurants:', error);
        }

        // Create system message with restaurant context
        const systemMessage = {
            role: 'system',
            content: `You are Jarvis, a helpful AI assistant for Torbaaz Food Delivery in Jahanian.

You help users find restaurants, browse menus, get food recommendations, and provide information about orders.

Available Restaurants and Menu Data:
${restaurantData}

Current Restaurant Information:
${restaurantsInfo}

Guidelines:
- Be friendly and helpful
- Provide specific information about restaurants and food items
- Help users make decisions about what to order
- If asked about prices, use the format "Rs. X"
- If users ask about restaurants, provide details like delivery time, rating, and specialties
- For menu questions, list items with prices
- Be conversational and engaging
- If you don't have specific information, be honest about it`
        };

        // Add user message to history
        conversationHistory.push({ role: 'user', content: message });

        // Prepare messages
        const messages = [systemMessage, ...conversationHistory];

        // Call OpenAI API
        const response = await openai.chat.completions.create({
            model: 'gpt-4o',
            messages: messages,
            max_tokens: 800,
            temperature: 0.7
        });

        // Get assistant response
        const assistantMessage = response.choices[0].message.content;

        // Add assistant response to history
        conversationHistory.push({ role: 'assistant', content: assistantMessage });

        // Update conversation history (keep last 10 messages to manage context)
        if (conversationHistory.length > 10) {
            conversationHistory.splice(0, conversationHistory.length - 10);
        }

        activeThreads.set(userId, conversationHistory);

        return assistantMessage;

    } catch (error) {
        console.error('Error querying Jarvis:', error);

        // Return a fallback response
        return "I'm sorry, I'm having trouble processing your request right now. Please try again in a moment.";
    }
}

/**
 * Clear a user's conversation thread
 * @param {string} userId
 */
async function clearThread(userId) {
    if (activeThreads.has(userId)) {
        activeThreads.delete(userId);
    }
}

module.exports = {
    queryJarvis,
    clearThread
};