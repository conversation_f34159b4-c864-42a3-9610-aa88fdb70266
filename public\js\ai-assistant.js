class AIAssistant {
    constructor() {
        this.container = document.querySelector('.chat-container');
        this.suggestionChips = document.querySelector('.suggestion-chips');
        this.restaurants = [];
        this.initializeEventListeners();
        this.loadRestaurantData();
    }

    async loadRestaurantData() {
        try {
            const response = await fetch('/data/ai_assistant_data.txt');
            const data = await response.text();
            this.restaurants = this.parseRestaurantData(data);
        } catch (error) {
            console.error('Error loading restaurant data:', error);
            this.restaurants = [];
        }
    }

    parseRestaurantData(data) {
        // Reuse the same parsing logic from main.js
        const restaurants = [];
        const lines = data.split('\n');
        let currentRestaurant = null;

        for (const line of lines) {
            if (line.startsWith('Restaurant:')) {
                if (currentRestaurant) {
                    restaurants.push(currentRestaurant);
                }
                currentRestaurant = {
                    name: line.replace('Restaurant:', '').trim(),
                    menu: []
                };
            } else if (line.includes('-')) {
                const [item, price] = line.split('-').map(s => s.trim());
                if (currentRestaurant && item && price) {
                    currentRestaurant.menu.push({
                        name: item,
                        price: price.replace('Rs.', '').trim()
                    });
                }
            }
        }

        if (currentRestaurant) {
            restaurants.push(currentRestaurant);
        }

        return restaurants;
    }

    initializeEventListeners() {
        // Handle suggestion chip clicks
        this.suggestionChips.addEventListener('click', (e) => {
            if (e.target.classList.contains('suggestion-chip')) {
                this.handleUserInput(e.target.textContent);
            }
        });

        // Add click listener to toggle AI assistant
        document.querySelector('.assistant-header').addEventListener('click', () => {
            document.querySelector('.ai-assistant').classList.toggle('minimized');
        });
    }

    addMessage(text, isUser = false) {
        const message = document.createElement('div');
        message.className = `message ${isUser ? 'user-message' : 'bot-message'} animate-slide-up`;
        message.textContent = text;
        this.container.appendChild(message);
        this.container.scrollTop = this.container.scrollHeight;
    }

    async handleUserInput(input) {
        this.addMessage(input, true);
        
        // Show typing indicator
        const typingIndicator = this.showTypingIndicator();
        
        // Process the input and generate response
        const response = await this.generateResponse(input);
        
        // Remove typing indicator and show response
        typingIndicator.remove();
        this.addMessage(response);
    }

    showTypingIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'message bot-message typing-indicator';
        indicator.innerHTML = '<span></span><span></span><span></span>';
        this.container.appendChild(indicator);
        return indicator;
    }

    async generateResponse(input) {
        const lowerInput = input.toLowerCase();
        
        // Handle different types of queries
        if (lowerInput.includes('find') || lowerInput.includes('restaurant')) {
            return this.handleRestaurantQuery(lowerInput);
        } else if (lowerInput.includes('deal') || lowerInput.includes('offer')) {
            return this.handleDealsQuery();
        } else if (lowerInput.includes('track') || lowerInput.includes('order')) {
            return "I can help you track your order. Please provide your order number.";
        } else if (lowerInput.includes('help')) {
            return "I can help you with:\n- Finding restaurants\n- Checking today's deals\n- Tracking orders\n- Menu recommendations";
        } else {
            return "I'm here to help! You can ask me about restaurants, deals, or tracking your order.";
        }
    }

    handleRestaurantQuery(query) {
        const restaurants = this.restaurants
            .map(r => r.name)
            .join('\n- ');
        return `Here are the available restaurants:\n- ${restaurants}`;
    }

    handleDealsQuery() {
        const deals = this.restaurants
            .flatMap(r => r.menu
                .slice(0, 1)
                .map(item => `${item.name} at ${r.name} - Rs. ${Math.floor(parseInt(item.price) * 0.8)}`)
            )
            .join('\n- ');
        return `Today's special deals:\n- ${deals}`;
    }
}

// Initialize AI Assistant when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.aiAssistant = new AIAssistant();
    // Show welcome message
    window.aiAssistant.addMessage("Hi! I'm Jarvis, your food assistant. How can I help you today?");
}); 