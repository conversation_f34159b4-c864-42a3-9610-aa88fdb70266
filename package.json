{"name": "torbaaz-web", "version": "1.0.0", "description": "Torbaaz Food Delivery Web Application", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js"}, "keywords": ["torb<PERSON>z", "food-delivery", "ai-assistant", "openai"], "author": "", "license": "ISC", "dependencies": {"@supabase/supabase-js": "^2.49.8", "dotenv": "^16.5.0", "express": "^4.18.2", "node-fetch": "^3.3.2", "openai": "^4.28.0"}, "devDependencies": {"autoprefixer": "^10.4.21", "nodemon": "^3.0.3", "postcss": "^8.5.4", "tailwindcss": "^4.1.8"}, "engines": {"node": ">=14.0.0"}}