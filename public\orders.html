<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Orders - Torbaaz Food Delivery</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/chat.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" type="image/png" href="favicon.png">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <div class="logo">
                <img src="/assets/images/logo.png" alt="Torbaaz Logo">
                <h1>Torbaaz</h1>
            </div>
            <div class="search-bar">
                <input type="text" placeholder="Search for restaurants or dishes...">
                <button><i class="fas fa-search"></i></button>
            </div>
            <ul class="nav-links">
                <li><a href="index.html">Home</a></li>
                <li><a href="#restaurants">Restaurants</a></li>
                <li><a href="deals.html">Deals</a></li>
                <li><a href="orders.html" class="active">Orders</a></li>
                <li><a href="about.html">About</a></li>
            </ul>
            <div class="cart-icon" onclick="viewCart()">
                <i class="fas fa-shopping-cart"></i>
                <span class="cart-badge">0</span>
            </div>
        </nav>
    </header>

    <!-- Orders Hero Section -->
    <section class="orders-hero">
        <div class="hero-content">
            <h1>My Orders</h1>
            <p>Track your orders and view order history</p>
        </div>
    </section>

    <!-- Orders Content -->
    <section class="orders-section">
        <div class="container">
            <!-- Order Filters -->
            <div class="order-filters">
                <button class="filter-btn active" data-status="all">All Orders</button>
                <button class="filter-btn" data-status="pending">Pending</button>
                <button class="filter-btn" data-status="preparing">Preparing</button>
                <button class="filter-btn" data-status="on_way">On the Way</button>
                <button class="filter-btn" data-status="delivered">Delivered</button>
                <button class="filter-btn" data-status="cancelled">Cancelled</button>
            </div>

            <!-- Orders List -->
            <div class="orders-list" id="ordersList">
                <!-- Sample orders for demonstration -->
                <div class="order-card" data-status="delivered">
                    <div class="order-header">
                        <div class="order-info">
                            <h3>Order #TRB001</h3>
                            <p class="order-date">December 15, 2024 - 2:30 PM</p>
                        </div>
                        <div class="order-status delivered">
                            <i class="fas fa-check-circle"></i>
                            <span>Delivered</span>
                        </div>
                    </div>
                    <div class="order-details">
                        <div class="restaurant-info">
                            <img src="images/placeholder.svg" alt="Restaurant" class="restaurant-logo">
                            <div>
                                <h4>Khana Khazana</h4>
                                <p>Traditional Pakistani Cuisine</p>
                            </div>
                        </div>
                        <div class="order-items">
                            <div class="item">
                                <span>Chicken Biryani x2</span>
                                <span>Rs. 720</span>
                            </div>
                            <div class="item">
                                <span>Chicken Karahi x1</span>
                                <span>Rs. 450</span>
                            </div>
                        </div>
                        <div class="order-total">
                            <strong>Total: Rs. 1,245 (including delivery)</strong>
                        </div>
                    </div>
                    <div class="order-actions">
                        <button class="reorder-btn" onclick="reorderItems('TRB001')">
                            <i class="fas fa-redo"></i> Reorder
                        </button>
                        <button class="rate-btn" onclick="rateOrder('TRB001')">
                            <i class="fas fa-star"></i> Rate Order
                        </button>
                    </div>
                </div>

                <div class="order-card" data-status="on_way">
                    <div class="order-header">
                        <div class="order-info">
                            <h3>Order #TRB002</h3>
                            <p class="order-date">December 15, 2024 - 4:15 PM</p>
                        </div>
                        <div class="order-status on_way">
                            <i class="fas fa-motorcycle"></i>
                            <span>On the Way</span>
                        </div>
                    </div>
                    <div class="order-details">
                        <div class="restaurant-info">
                            <img src="images/placeholder.svg" alt="Restaurant" class="restaurant-logo">
                            <div>
                                <h4>Pizza Corner</h4>
                                <p>Italian & Fast Food</p>
                            </div>
                        </div>
                        <div class="order-items">
                            <div class="item">
                                <span>Margherita Pizza (Large) x1</span>
                                <span>Rs. 850</span>
                            </div>
                            <div class="item">
                                <span>Garlic Bread x1</span>
                                <span>Rs. 200</span>
                            </div>
                        </div>
                        <div class="order-total">
                            <strong>Total: Rs. 1,125 (including delivery)</strong>
                        </div>
                        <div class="delivery-tracking">
                            <div class="tracking-info">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Estimated delivery: 15-20 minutes</span>
                            </div>
                            <button class="track-btn" onclick="trackOrder('TRB002')">
                                <i class="fas fa-route"></i> Track Order
                            </button>
                        </div>
                    </div>
                </div>

                <div class="order-card" data-status="preparing">
                    <div class="order-header">
                        <div class="order-info">
                            <h3>Order #TRB003</h3>
                            <p class="order-date">December 15, 2024 - 5:00 PM</p>
                        </div>
                        <div class="order-status preparing">
                            <i class="fas fa-utensils"></i>
                            <span>Preparing</span>
                        </div>
                    </div>
                    <div class="order-details">
                        <div class="restaurant-info">
                            <img src="images/placeholder.svg" alt="Restaurant" class="restaurant-logo">
                            <div>
                                <h4>Burger House</h4>
                                <p>American Fast Food</p>
                            </div>
                        </div>
                        <div class="order-items">
                            <div class="item">
                                <span>Classic Burger x2</span>
                                <span>Rs. 600</span>
                            </div>
                            <div class="item">
                                <span>French Fries x2</span>
                                <span>Rs. 300</span>
                            </div>
                        </div>
                        <div class="order-total">
                            <strong>Total: Rs. 975 (including delivery)</strong>
                        </div>
                        <div class="preparation-time">
                            <i class="fas fa-clock"></i>
                            <span>Estimated preparation time: 10-15 minutes</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Empty State -->
            <div class="empty-orders" style="display: none;">
                <i class="fas fa-receipt"></i>
                <h3>No orders found</h3>
                <p>You haven't placed any orders yet. Start exploring our restaurants!</p>
                <a href="index.html" class="browse-restaurants-btn">Browse Restaurants</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-section">
                <h3>About Us</h3>
                <ul>
                    <li><a href="about.html">Our Story</a></li>
                    <li><a href="#">Blog</a></li>
                    <li><a href="#">Careers</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>For Restaurants</h3>
                <ul>
                    <li><a href="#">Partner with us</a></li>
                    <li><a href="#">Restaurant app</a></li>
                    <li><a href="#">Business Blog</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Support</h3>
                <ul>
                    <li><a href="#">Help Center</a></li>
                    <li><a href="#">Contact us</a></li>
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Follow Us</h3>
                <ul>
                    <li><a href="#"><i class="fab fa-facebook"></i> Facebook</a></li>
                    <li><a href="#"><i class="fab fa-instagram"></i> Instagram</a></li>
                    <li><a href="#"><i class="fab fa-twitter"></i> Twitter</a></li>
                </ul>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 Torbaaz Food Delivery. All rights reserved.</p>
        </div>
    </footer>

    <!-- Chat Widget -->
    <div class="chat-widget minimized">
        <div class="chat-toggle">
            <!-- Content will be dynamically generated by JavaScript -->
        </div>
        <div class="chat-content">
            <div class="chat-container">
                <!-- Messages will be dynamically added here -->
            </div>
            <div class="suggestion-chips">
                <button class="suggestion-chip">Track my order</button>
                <button class="suggestion-chip">Order history</button>
                <button class="suggestion-chip">Reorder previous meal</button>
            </div>
            <div class="chat-input">
                <textarea class="message-input" placeholder="Ask about your orders..."></textarea>
                <button class="send-button">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script src="js/chat.js"></script>
    <script src="js/search.js"></script>
    <script src="js/orders.js"></script>
</body>
</html>
