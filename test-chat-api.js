const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testChatAPI() {
    try {
        console.log('Testing Chat API...');
        
        const response = await fetch('http://localhost:3000/api/chat/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                userId: 'test_chat_widget',
                text: 'Hello <PERSON>, can you help me find food?'
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ Chat API Response:', data.reply);
        } else {
            console.log('❌ Chat API Error:', response.status, response.statusText);
        }
    } catch (error) {
        console.error('❌ Chat API Connection Error:', error.message);
    }
}

testChatAPI();
