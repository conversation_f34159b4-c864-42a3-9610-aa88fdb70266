/* Chat Widget - Enhanced Animation System */
.chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    height: 500px;
    min-width: 300px;
    min-height: 400px;
    max-width: 90vw;
    max-height: 90vh;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: bottom right;
    overflow: hidden;
    border: 2px solid #ff6b35;
    display: flex;
    flex-direction: column;
}

.chat-widget.dragging {
    transition: none;
    user-select: none;
}

/* Chat Widget States */
.chat-widget.chat-collapsed {
    width: 350px;
    height: 500px;
    bottom: 20px;
    right: 20px;
    top: auto;
    left: auto;
    z-index: 1000;
    transform: scale(1);
    transform-origin: bottom right;
}

.chat-widget.chat-expanded {
    width: 90vw;
    height: 90vh;
    top: 5vh;
    left: 5vw;
    right: auto;
    bottom: auto;
    z-index: 9999;
    transform: scale(1);
    transform-origin: center;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    border-radius: 12px;
}

.chat-widget.minimized {
    width: auto;
    height: auto;
    max-height: 60px;
    overflow: hidden;
    transform: scale(1);
}

/* Modal Backdrop for Expanded State */
.chat-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(4px);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
                visibility 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-backdrop.active {
    opacity: 1;
    visibility: visible;
}

/* Enhanced Content Animation */
.chat-widget.minimized .chat-content {
    display: none !important;
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-widget:not(.minimized) .chat-content {
    display: flex !important;
    opacity: 1 !important;
    transform: translateY(0) scale(1) !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    visibility: visible !important;
}

.chat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(100% - 60px);
    min-height: 400px;
    background: white;
    border-radius: 0 0 15px 15px;
    transition: border-radius 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced content for expanded state */
.chat-widget.chat-expanded .chat-content {
    border-radius: 0 0 12px 12px;
}

.chat-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #FF6B00;
    color: white;
    cursor: pointer;
    user-select: none;
}

.chat-toggle:hover {
    background: #e65100;
}

.chat-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.chat-controls {
    display: flex;
    gap: 8px;
}

.chat-control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.chat-control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Enhanced button animations */
.chat-control-btn i {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-control-btn:hover i {
    transform: scale(1.1);
}

/* Close button for expanded state */
.chat-close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 1rem;
}

.chat-widget.chat-expanded .chat-close-btn {
    display: flex;
}

.chat-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 20px;
    height: 20px;
    background: linear-gradient(-45deg, transparent 0%, transparent 40%, #ff6b35 40%, #ff6b35 60%, transparent 60%);
    cursor: nw-resize;
    border-bottom-right-radius: 15px;
    z-index: 10;
}

/* Drag and Resize States */
.chat-widget.dragging {
    user-select: none;
    transition: none !important;
    z-index: 10001;
}

.chat-widget.dragging * {
    pointer-events: none;
}

.chat-widget.resizing {
    user-select: none;
    transition: none !important;
}

.chat-title {
    cursor: move;
    user-select: none;
}

.chat-widget.dragging .chat-title {
    cursor: grabbing;
}

.chat-widget.maximized {
    border-radius: 8px !important;
}

.chat-widget.maximized .chat-content {
    border-radius: 0 0 8px 8px !important;
}

.chat-widget.maximized .resize-handle {
    display: none;
}

.chat-toggle i {
    font-size: 1.2rem;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-height: 200px;
    background: #fafafa;
    border-radius: 8px;
    margin: 0.5rem;
}

.message {
    margin-bottom: 1rem;
    max-width: 80%;
    padding: 0.8rem 1rem;
    border-radius: 15px;
    position: relative;
    opacity: 1 !important;
    transform: translateY(0) !important;
    animation: slideIn 0.3s forwards;
}

.message.animate-in {
    opacity: 0;
    transform: translateY(20px);
    animation: slideIn 0.3s forwards;
}

.bot-message {
    background: #f5f5f5;
    margin-right: auto;
    border-radius: 15px 15px 15px 0;
}

.user-message {
    background: var(--primary-color);
    color: white;
    margin-left: auto;
    border-radius: 15px 15px 0 15px;
}

.suggestion-chips {
    padding: 1rem;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    border-top: 1px solid #eee;
}

.suggestion-chip {
    background: #f5f5f5;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.suggestion-chip:hover {
    background: #e0e0e0;
}

.chat-input {
    padding: 1rem;
    border-top: 1px solid #eee;
    display: flex;
    gap: 0.5rem;
}

.message-input {
    flex: 1;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 25px;
    resize: none;
    height: 45px;
    font-family: inherit;
    font-size: 0.9rem;
    line-height: 1.4;
}

.message-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.send-button {
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.send-button:hover {
    background: #e65100;
}

/* Animations */
@keyframes slideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading Indicator */
.typing-indicator {
    display: flex;
    gap: 0.3rem;
    padding: 1rem;
    background: #f5f5f5;
    border-radius: 15px 15px 15px 0;
    width: fit-content;
}

.typing-indicator span {
    width: 8px;
    height: 8px;
    background: #999;
    border-radius: 50%;
    animation: typing 1s infinite;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
}

/* Message Highlights */
.highlight-price {
    color: #4CAF50;
    font-weight: bold;
}

.highlight-restaurant {
    color: var(--primary-color);
    font-weight: bold;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .chat-widget.chat-collapsed {
        width: 320px;
        height: 450px;
        bottom: 10px;
        right: 10px;
    }

    .chat-widget.chat-expanded {
        width: 95vw;
        height: 95vh;
        top: 2.5vh;
        left: 2.5vw;
        border-radius: 8px;
    }

    .chat-widget.minimized {
        width: auto;
        height: auto;
        bottom: 10px;
        right: 10px;
        border-radius: 15px;
    }

    .chat-content {
        height: calc(100% - 60px);
    }

    /* Adjust backdrop for mobile */
    .chat-backdrop {
        backdrop-filter: blur(2px);
    }
}

@media (max-width: 480px) {
    .chat-widget.chat-collapsed {
        width: 280px;
        height: 400px;
    }

    .chat-widget.chat-expanded {
        width: 100vw;
        height: 100vh;
        top: 0;
        left: 0;
        border-radius: 0;
    }

    .chat-widget.chat-expanded .chat-content {
        border-radius: 0;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .chat-widget,
    .chat-content,
    .chat-backdrop,
    .chat-control-btn {
        transition: none !important;
        animation: none !important;
    }
}