:root {
    --primary-color: #FF6B00;
    --secondary-color: #333;
    --background-color: #f5f5f5;
    --text-color: #333;
    --border-radius: 10px;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
}

/* Header Styles */
header {
    background: white;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

nav {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo img {
    height: 80px;
    width: auto;
    border-radius: 12px;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

.logo h1 {
    color: var(--primary-color);
    font-size: 2.5rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    transition: color 0.3s ease;
}

.logo:hover h1 {
    color: #e55a2b;
}

.search-bar {
    flex: 1;
    max-width: 500px;
    margin: 0 2rem;
    position: relative;
}

.search-bar input {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.search-bar input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.search-bar button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.6rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-bar button:hover {
    background: #e65100;
}

/* Search Suggestions */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 0 0 15px 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
}

.suggestion-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    cursor: pointer;
    transition: background 0.3s ease;
    border-bottom: 1px solid #f0f0f0;
}

.suggestion-item:hover {
    background: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item i {
    color: var(--primary-color);
    width: 20px;
    text-align: center;
}

.suggestion-item span {
    flex: 1;
    font-weight: 500;
}

.suggestion-item small {
    color: #666;
    font-size: 0.8rem;
}

/* Search Results */
.search-results {
    position: fixed;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 800px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    max-height: 80vh;
    overflow-y: auto;
}

.search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    background: var(--primary-color);
    color: white;
    border-radius: 15px 15px 0 0;
}

.search-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.close-search {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-search:hover {
    background: rgba(255, 255, 255, 0.3);
}

.search-content {
    padding: 1.5rem;
}

.search-section {
    margin-bottom: 2rem;
}

.search-section h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.search-items {
    display: grid;
    gap: 1rem;
}

.search-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #eee;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.search-item img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.item-info {
    flex: 1;
}

.item-info h5 {
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
    font-size: 1.1rem;
}

.item-info p {
    margin: 0 0 0.5rem 0;
    color: #666;
    font-size: 0.9rem;
}

.item-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: #666;
}

.item-meta .price {
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1rem;
}

.item-meta .discount {
    background: #e74c3c;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
}

.no-results {
    text-align: center;
    padding: 3rem 1rem;
    color: #666;
}

.no-results i {
    font-size: 3rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.no-results h4 {
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

/* Navigation Styles */
.nav-links {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    color: #4B5563;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: #FF6B00;
    background: rgba(255, 107, 0, 0.1);
}

.nav-link.active {
    color: #FF6B00;
    background: rgba(255, 107, 0, 0.1);
    font-weight: 600;
}

/* Cart Button */
.cart-btn {
    position: relative;
    background: none;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
    color: #4B5563;
    transition: color 0.3s ease;
}

.cart-btn:hover {
    color: #FF6B00;
}

.cart-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #FF6B00;
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    min-width: 1.5rem;
    text-align: center;
}

/* Filter Buttons */
.filter-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #E5E7EB;
    border-radius: 0.5rem;
    background: white;
    color: #4B5563;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    border-color: #FF6B00;
    color: #FF6B00;
    background: rgba(255, 107, 0, 0.1);
}

.filter-btn.active {
    background: #FF6B00;
    color: white;
    border-color: #FF6B00;
}

/* Category Buttons */
.category-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #E5E7EB;
    border-radius: 0.5rem;
    background: white;
    color: #4B5563;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.category-btn:hover {
    border-color: #FF6B00;
    color: #FF6B00;
    background: rgba(255, 107, 0, 0.1);
}

.category-btn.active {
    background: #FF6B00;
    color: white;
    border-color: #FF6B00;
}

/* Responsive Navigation */
@media (max-width: 768px) {
    .nav-links {
        gap: 1rem;
    }

    .nav-link {
        padding: 0.5rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 640px) {
    .nav-links {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        padding: 1rem;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        justify-content: space-around;
        z-index: 50;
    }

    .nav-link {
        flex: 1;
        text-align: center;
        padding: 0.5rem;
        font-size: 0.75rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
    }

    .nav-link i {
        font-size: 1.25rem;
    }
}

/* Enhanced Hero Section */
.hero-modern {
    position: relative;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(255, 107, 0, 0.9) 0%,
        rgba(255, 107, 0, 0.7) 50%,
        rgba(0, 0, 0, 0.6) 100%
    );
    z-index: 2;
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.hero-content-modern {
    position: relative;
    z-index: 3;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
}

.hero-text {
    max-width: 800px;
    color: white;
}

.hero-title {
    margin-bottom: 1.5rem;
}

.hero-title-main {
    display: block;
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-title-sub {
    display: block;
    font-size: clamp(1.5rem, 3vw, 2.5rem);
    font-weight: 400;
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.hero-description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    opacity: 0.95;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 2.5rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    opacity: 0.9;
    margin-top: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-primary, .cta-secondary {
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    border: none;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    min-width: 180px;
    justify-content: center;
}

.cta-primary {
    background: #fff;
    color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.cta-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    background: #f8f9fa;
}

.cta-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.cta-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px);
}

/* Category Section */
.category-section {
    position: relative;
    z-index: 3;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2rem 0;
    margin-top: auto;
}

.category-title {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 1.5rem;
}

.category-chips-modern {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    max-width: 800px;
    margin: 0 auto;
}

.category-chip-modern {
    background: white;
    border: 2px solid #e0e0e0;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.category-chip-modern i {
    font-size: 1.1em;
    color: var(--primary-color);
}

.category-chip-modern:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(255, 107, 0, 0.2);
}

.category-chip-modern.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.category-chip-modern.active i {
    color: white;
}

/* Modern Section Styles */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.section-title i {
    color: var(--primary-color);
    font-size: 2rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Featured Section */
.featured-section {
    padding: 4rem 0;
    background: #f8f9fa;
}

.featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.featured-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.featured-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.featured-item-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.featured-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.featured-item:hover .featured-item-image img {
    transform: scale(1.05);
}

.featured-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.featured-item-content {
    padding: 1.5rem;
}

.featured-item-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.featured-item-restaurant {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.featured-item-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.featured-item-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.featured-item-rating {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    color: #ffc107;
    font-weight: 600;
}

.featured-item-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-add-to-cart {
    flex: 1;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.8rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-add-to-cart:hover {
    background: #e55a2b;
    transform: translateY(-1px);
}

.btn-favorite {
    background: #f8f9fa;
    border: 2px solid #e0e0e0;
    color: #666;
    width: 45px;
    height: 45px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-favorite:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-favorite.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Deals Section Modern */
.deals-section-modern {
    padding: 4rem 0;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: white;
}

.deals-section-modern .section-title,
.deals-section-modern .section-subtitle {
    color: white;
}

.deals-timer {
    background: rgba(255, 255, 255, 0.2);
    padding: 1rem 2rem;
    border-radius: 25px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    backdrop-filter: blur(10px);
    font-weight: 600;
}

.deals-container-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.deal-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.deal-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

.deal-card-image {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.deal-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.deal-discount {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #e74c3c;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
}

.deal-card-content {
    padding: 1.5rem;
    color: var(--text-color);
}

.deal-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.deal-restaurant {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.deal-pricing {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.deal-price-current {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary-color);
}

.deal-price-original {
    font-size: 1rem;
    color: #999;
    text-decoration: line-through;
}

.deal-savings {
    background: #e8f5e8;
    color: #2e7d32;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.deal-actions {
    display: flex;
    gap: 0.5rem;
}

/* Restaurants Section Modern */
.restaurants-section-modern {
    padding: 4rem 0;
    background: #f8f9fa;
}

.filter-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.filter-group {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.view-toggle {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    background: white;
    border: 2px solid #e0e0e0;
    color: #666;
    width: 45px;
    height: 45px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.view-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.restaurants-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.restaurants-list-modern {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-top: 2rem;
}

.restaurant-card-modern {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.restaurant-card-modern:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.restaurant-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.restaurant-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.restaurant-card-modern:hover .restaurant-image img {
    transform: scale(1.05);
}

.restaurant-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.restaurant-status.open {
    background: #e8f5e8;
    color: #2e7d32;
}

.restaurant-status.closed {
    background: #ffebee;
    color: #c62828;
}

.restaurant-content {
    padding: 1.5rem;
}

.restaurant-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.restaurant-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.restaurant-rating {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    color: #ffc107;
    font-weight: 600;
    background: #fff8e1;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
}

.restaurant-cuisine {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.restaurant-meta {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: #666;
}

.restaurant-meta span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.restaurant-meta i {
    color: var(--primary-color);
}

.restaurant-tags {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.tag {
    background: #f0f0f0;
    color: #666;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.restaurant-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-view-menu {
    flex: 1;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.8rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-view-menu:hover {
    background: #e55a2b;
    transform: translateY(-1px);
}

.load-more-section {
    text-align: center;
    margin-top: 3rem;
}

.load-more-btn {
    background: white;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 1rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.load-more-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Newsletter Section */
.newsletter-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.newsletter-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    flex-wrap: wrap;
}

.newsletter-text h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.newsletter-text p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    gap: 1rem;
    min-width: 400px;
}

.newsletter-form input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    outline: none;
}

.newsletter-form button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
}

.newsletter-form button:hover {
    background: #e55a2b;
    transform: translateY(-2px);
}

/* Responsive Design for Modern Components */
@media (max-width: 768px) {
    .hero-stats {
        gap: 2rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .cta-primary, .cta-secondary {
        width: 100%;
        max-width: 300px;
    }

    .category-chips-modern {
        gap: 0.5rem;
    }

    .category-chip-modern {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .featured-grid,
    .deals-container-modern,
    .restaurants-grid-modern {
        grid-template-columns: 1fr;
    }

    .filter-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .newsletter-content {
        flex-direction: column;
        text-align: center;
    }

    .newsletter-form {
        min-width: auto;
        width: 100%;
    }

    .newsletter-form input {
        min-width: 0;
    }
}

@media (max-width: 480px) {
    .hero-title-main {
        font-size: 2rem;
    }

    .hero-title-sub {
        font-size: 1.2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .newsletter-form {
        flex-direction: column;
    }
}

/* Cart Page Styles */
.cart-page {
    padding: 2rem 0;
    min-height: 70vh;
    background: #f8f9fa;
}

.cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
}

.page-title i {
    color: var(--primary-color);
}

.clear-cart-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.clear-cart-btn:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.cart-layout {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 2rem;
    align-items: start;
}

.cart-items-section {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.cart-items-container {
    min-height: 200px;
}

.empty-cart {
    text-align: center;
    padding: 3rem 1rem;
    color: #666;
}

.empty-cart i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.empty-cart h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.empty-cart p {
    margin-bottom: 2rem;
}

.browse-btn {
    background: var(--primary-color);
    color: white;
    padding: 1rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.browse-btn:hover {
    background: #e55a2b;
    transform: translateY(-2px);
}

.restaurant-group {
    margin-bottom: 2rem;
}

.restaurant-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 1rem;
}

.restaurant-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.restaurant-header i {
    color: var(--primary-color);
}

.item-count {
    background: var(--primary-color);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.cart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    margin-bottom: 1rem;
    background: white;
    transition: all 0.3s ease;
}

.cart-item:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.item-image {
    width: 80px;
    height: 80px;
    border-radius: 10px;
    overflow: hidden;
    flex-shrink: 0;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-details {
    flex: 1;
}

.item-details h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 0.5rem 0;
}

.item-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.item-price {
    font-weight: 600;
    color: var(--primary-color);
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 0.5rem;
}

.qty-btn {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 50%;
    background: white;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.qty-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.quantity {
    min-width: 30px;
    text-align: center;
    font-weight: 600;
    color: var(--text-color);
}

.item-total {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-color);
    min-width: 80px;
    text-align: right;
}

.remove-item {
    background: #dc3545;
    color: white;
    border: none;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-item:hover {
    background: #c82333;
    transform: scale(1.1);
}

/* Cart Summary Styles */
.cart-summary-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.cart-summary {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 2rem;
}

.summary-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 1rem;
}

.summary-details {
    margin-bottom: 1.5rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.summary-row:last-child {
    border-bottom: none;
}

.total-row {
    border-top: 2px solid #e0e0e0;
    padding-top: 1rem;
    margin-top: 1rem;
    font-weight: 700;
    font-size: 1.1rem;
}

.total-amount {
    color: var(--primary-color);
    font-size: 1.3rem;
}

.discount-amount {
    color: #28a745;
}

.promo-section {
    margin-bottom: 1.5rem;
}

.promo-section h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.promo-input-group {
    display: flex;
    gap: 0.5rem;
}

.promo-input-group input {
    flex: 1;
    padding: 0.8rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 0.9rem;
    outline: none;
    transition: border-color 0.3s ease;
}

.promo-input-group input:focus {
    border-color: var(--primary-color);
}

.apply-promo-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.8rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.apply-promo-btn:hover {
    background: #e55a2b;
}

.checkout-btn {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.checkout-btn:hover:not(:disabled) {
    background: #e55a2b;
    transform: translateY(-2px);
}

.checkout-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* Recommended Items */
.recommended-section {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.recommended-section h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-color);
}

.recommended-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.recommended-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.recommended-item:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.recommended-item img {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    object-fit: cover;
}

.recommended-info {
    flex: 1;
}

.recommended-info h5 {
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0 0 0.3rem 0;
    color: var(--text-color);
}

.recommended-price {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
}

.add-recommended {
    background: var(--primary-color);
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.add-recommended:hover {
    background: #e55a2b;
    transform: scale(1.1);
}

/* Breadcrumbs */
.breadcrumbs-container {
    background: white;
    border-bottom: 1px solid #e0e0e0;
    padding: 1rem 0;
}

.breadcrumbs {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: #e55a2b;
}

.breadcrumb-item.active {
    color: #666;
}

.breadcrumbs i {
    color: #ccc;
    font-size: 0.8rem;
}

/* Responsive Design for Cart */
@media (max-width: 768px) {
    .cart-layout {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .cart-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .page-title {
        font-size: 2rem;
        justify-content: center;
    }

    .cart-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .item-details {
        text-align: center;
    }

    .quantity-controls {
        justify-content: center;
    }

    .promo-input-group {
        flex-direction: column;
    }

    .apply-promo-btn {
        width: 100%;
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--primary-color);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    z-index: 10000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    max-width: 350px;
}

.notification.show {
    transform: translateX(0);
}

.notification.error {
    background: #dc3545;
}

.notification.success {
    background: #28a745;
}

.notification i {
    font-size: 1.2rem;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.modal-header h2 {
    margin: 0;
    color: var(--text-color);
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-modal:hover {
    background: #f0f0f0;
    color: var(--text-color);
}

.modal-body {
    padding: 1.5rem;
}

.form-section {
    margin-bottom: 2rem;
}

.form-section h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-color);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
}

.payment-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.payment-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-option:hover {
    border-color: var(--primary-color);
}

.payment-option input[type="radio"] {
    width: auto;
    margin: 0;
}

.order-summary-checkout {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 1.5rem;
}

.order-summary-checkout h3 {
    margin-bottom: 1rem;
    color: var(--text-color);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e0e0e0;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-item.total {
    font-weight: 700;
    font-size: 1.1rem;
    border-top: 2px solid #e0e0e0;
    padding-top: 1rem;
    margin-top: 1rem;
}

.summary-item.discount {
    color: #28a745;
}

.place-order-btn {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.place-order-btn:hover {
    background: #e55a2b;
    transform: translateY(-2px);
}

/* Deals Section */
.deals-section {
    padding: 3rem 1rem;
    max-width: 1200px;
    margin: 0 auto;
}

.deals-section h2 {
    margin-bottom: 2rem;
    text-align: center;
    font-size: 2rem;
}

.deals-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.deal-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
    position: relative;
}

.deal-card:hover {
    transform: translateY(-5px);
}

.deal-image {
    position: relative;
    height: 200px;
}

.deal-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: bold;
}

.deal-content {
    padding: 1.5rem;
}

.deal-content h3 {
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.deal-content .restaurant {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.deal-content .description {
    color: #444;
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.price {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.price .original {
    color: #999;
    text-decoration: line-through;
    font-size: 0.9rem;
}

.price .discounted {
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.1rem;
}

.order-now {
    width: 100%;
    padding: 0.8rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s ease;
}

.order-now:hover {
    background: #e65100;
}

/* Restaurants Section */
.restaurants-section {
    padding: 3rem 1rem;
    max-width: 1200px;
    margin: 0 auto;
}

.restaurants-section h2 {
    margin-bottom: 2rem;
    text-align: center;
    font-size: 2rem;
}

.restaurants-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.restaurant-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.restaurant-card:hover {
    transform: translateY(-5px);
}

.card-image {
    position: relative;
    height: 200px;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.rating {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 25px;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.rating i {
    color: #FFD700;
}

.card-content {
    padding: 1.5rem;
}

.card-content h3 {
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.cuisine {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.address {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    color: #444;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.address i {
    color: var(--primary-color);
    margin-top: 0.2rem;
}

.card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.contact {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.9rem;
}

.contact i {
    color: var(--primary-color);
}

.view-menu {
    padding: 0.5rem 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s ease;
}

.view-menu:hover {
    background: #e65100;
}

/* No Results */
.no-results {
    text-align: center;
    padding: 2rem;
    color: #666;
    font-size: 1.1rem;
    grid-column: 1 / -1;
}

/* Footer */
footer {
    background: var(--secondary-color);
    color: white;
    padding: 3rem 1rem 1rem;
    margin-top: 3rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: white;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    max-width: 1200px;
    margin: 2rem auto 0;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    nav {
        flex-direction: column;
        gap: 1rem;
    }

    .search-bar {
        margin: 1rem 0;
        width: 100%;
    }

    .nav-links {
        width: 100%;
        justify-content: space-around;
    }

    .hero h2 {
        font-size: 2rem;
    }

    .category-chips {
        gap: 0.5rem;
    }

    .category-chip {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .deals-container,
    .restaurants-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 480px) {
    .nav-links {
        flex-wrap: wrap;
        gap: 1rem;
    }

    .hero {
        padding: 2rem 1rem;
    }

    .hero h2 {
        font-size: 1.5rem;
    }

    .deals-container,
    .restaurants-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .card-footer {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .view-menu {
        width: 100%;
    }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius);
    max-width: 800px;
    max-height: 90vh;
    width: 90%;
    overflow-y: auto;
    animation: slideUp 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
}

.modal-header h2 {
    margin: 0;
    color: var(--primary-color);
}

.close-modal {
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: var(--primary-color);
}

.modal-body {
    padding: 1.5rem;
}

.restaurant-info {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.restaurant-info img {
    width: 200px;
    height: 150px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.info-details p {
    margin: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-details i {
    color: var(--primary-color);
    width: 20px;
}

.menu-section h3 {
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

.menu-items {
    display: grid;
    gap: 1rem;
}

.menu-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #eee;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.menu-item:hover {
    box-shadow: var(--shadow);
    transform: translateY(-2px);
}

.item-info h4 {
    margin: 0 0 0.5rem 0;
    color: var(--secondary-color);
}

.item-description {
    color: #666;
    font-size: 0.9rem;
    margin: 0.5rem 0;
}

.item-category {
    color: var(--primary-color);
    font-size: 0.8rem;
    font-weight: 500;
    margin: 0;
}

.item-price {
    text-align: right;
}

.item-price span {
    display: block;
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.add-to-cart {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s ease;
}

.add-to-cart:hover {
    background: #e65100;
}

/* Loading Spinner */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error and Empty States */
.error-state, .empty-state {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.error-state i, .empty-state i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 1rem;
}

.retry-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    margin-top: 1rem;
    transition: background 0.3s ease;
}

.retry-btn:hover {
    background: #e65100;
}

/* Notification */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--primary-color);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 1001;
}

.notification.show {
    transform: translateX(0);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* About Page Styles */
.about-hero {
    background: linear-gradient(135deg, var(--primary-color), #ff6b35);
    color: white;
    padding: 4rem 1rem;
    text-align: center;
}

.about-hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.about-content {
    padding: 4rem 1rem;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    margin-bottom: 4rem;
}

.about-text h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.about-text h3 {
    color: var(--text-color);
    margin: 2rem 0 1rem;
}

.features-list {
    list-style: none;
    padding: 0;
}

.features-list li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.features-list i {
    color: var(--primary-color);
}

.about-image img {
    width: 100%;
    border-radius: var(--border-radius);
}

.stats-section {
    text-align: center;
    margin-bottom: 4rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
}

.stat-card i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.stat-card h3 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.team-section {
    text-align: center;
    margin-bottom: 4rem;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.team-member {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
}

.member-image i {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.contact-section {
    background: #f8f9fa;
    padding: 3rem;
    border-radius: var(--border-radius);
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-top: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.contact-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.contact-form button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background 0.3s ease;
}

.contact-form button:hover {
    background: #e55a2b;
}

/* Deals Page Styles */
.deals-hero {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    padding: 4rem 1rem;
    text-align: center;
}

.deals-hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.deals-filters {
    background: #f8f9fa;
    padding: 2rem 1rem;
    border-bottom: 1px solid #eee;
}

.filter-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.filter-group label {
    font-weight: 600;
    color: var(--text-color);
}

.filter-group select {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    background: white;
}

.category-filters {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: white;
    border: 2px solid #ddd;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.deals-section {
    padding: 3rem 1rem;
}

.deals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.deal-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.deal-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.discount-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: #e74c3c;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: bold;
    font-size: 0.9rem;
    z-index: 2;
}

.deal-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.deal-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.deal-timer {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.deal-content {
    padding: 1.5rem;
}

.deal-header h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.restaurant-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.deal-description {
    color: var(--text-color);
    line-height: 1.5;
    margin-bottom: 1.5rem;
}

.deal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.price-section {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.original-price {
    color: #999;
    text-decoration: line-through;
    font-size: 0.9rem;
}

.deal-price {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: bold;
}

.order-deal-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    transition: background 0.3s ease;
}

.order-deal-btn:hover {
    background: #e55a2b;
}

.featured-restaurants {
    background: #f8f9fa;
    padding: 3rem 1rem;
}

.featured-restaurants h2 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 2rem;
}

/* Cart Page Styles */
.cart-hero {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    padding: 3rem 1rem;
    text-align: center;
}

.cart-hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cart-section {
    padding: 3rem 1rem;
    background: #f8f9fa;
    min-height: 60vh;
}

.cart-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    max-width: 1200px;
    margin: 0 auto;
}

.cart-items-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #eee;
}

.clear-cart-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background 0.3s ease;
}

.clear-cart-btn:hover {
    background: #c0392b;
}

.restaurant-group {
    margin-bottom: 2rem;
    border: 1px solid #eee;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.restaurant-header {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
}

.restaurant-header h3 {
    margin: 0;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.item-count {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.cart-items {
    padding: 1rem;
}

.cart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
    transition: background 0.3s ease;
}

.cart-item:hover {
    background: #f8f9fa;
}

.cart-item:last-child {
    border-bottom: none;
}

.item-image img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.item-details {
    flex: 1;
}

.item-details h4 {
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
}

.item-description {
    color: #666;
    font-size: 0.9rem;
    margin: 0 0 0.5rem 0;
}

.item-price {
    color: var(--primary-color);
    font-weight: bold;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 0.5rem;
}

.qty-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.qty-btn:hover {
    background: #e55a2b;
}

.quantity {
    min-width: 30px;
    text-align: center;
    font-weight: bold;
}

.item-total {
    font-weight: bold;
    color: var(--primary-color);
    font-size: 1.1rem;
    min-width: 80px;
    text-align: right;
}

.remove-item {
    background: #e74c3c;
    color: white;
    border: none;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.remove-item:hover {
    background: #c0392b;
}

.empty-cart {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
}

.empty-cart i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.browse-btn {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    display: inline-block;
    margin-top: 1rem;
    transition: background 0.3s ease;
}

.browse-btn:hover {
    background: #e55a2b;
}

.order-summary-section {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.order-summary {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow);
    position: sticky;
    top: 2rem;
}

.order-summary h3 {
    margin: 0 0 1.5rem 0;
    color: var(--primary-color);
    text-align: center;
}

.summary-details {
    margin-bottom: 2rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    padding: 0.5rem 0;
}

.summary-row.total-row {
    font-weight: bold;
    font-size: 1.2rem;
    color: var(--primary-color);
    border-top: 2px solid #eee;
    padding-top: 1rem;
    margin-top: 1rem;
}

.promo-section {
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: var(--border-radius);
}

.promo-section h4 {
    margin: 0 0 1rem 0;
    color: var(--text-color);
}

.promo-input {
    display: flex;
    gap: 0.5rem;
}

.promo-input input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
}

.promo-input button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background 0.3s ease;
}

.promo-input button:hover {
    background: #e55a2b;
}

.checkout-btn {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: background 0.3s ease;
}

.checkout-btn:hover:not(:disabled) {
    background: #e55a2b;
}

.checkout-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.recommended-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.recommended-section h4 {
    margin: 0 0 1rem 0;
    color: var(--primary-color);
}

.recommended-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #eee;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.recommended-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.recommended-item img {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.recommended-info {
    flex: 1;
}

.recommended-info h5 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
}

.recommended-price {
    color: var(--primary-color);
    font-weight: bold;
    font-size: 0.9rem;
}

.add-recommended {
    background: var(--primary-color);
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.add-recommended:hover {
    background: #e55a2b;
}

/* Orders Page Styles */
.orders-hero {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
    padding: 3rem 1rem;
    text-align: center;
}

.orders-hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.orders-section {
    padding: 3rem 1rem;
    background: #f8f9fa;
    min-height: 60vh;
}

.order-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    justify-content: center;
}

.order-filters .filter-btn {
    background: white;
    border: 2px solid #ddd;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.order-filters .filter-btn.active,
.order-filters .filter-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.orders-list {
    max-width: 800px;
    margin: 0 auto;
}

.order-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.order-card:hover {
    transform: translateY(-2px);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
}

.order-info h3 {
    margin: 0 0 0.5rem 0;
    color: var(--primary-color);
    font-size: 1.3rem;
}

.order-date {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.order-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.9rem;
}

.order-status.pending {
    background: #fff3cd;
    color: #856404;
}

.order-status.preparing {
    background: #d1ecf1;
    color: #0c5460;
}

.order-status.on_way {
    background: #d4edda;
    color: #155724;
}

.order-status.delivered {
    background: #d1ecf1;
    color: #0c5460;
}

.order-status.cancelled {
    background: #f8d7da;
    color: #721c24;
}

.order-details {
    padding: 1.5rem;
}

.restaurant-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f0f0f0;
}

.restaurant-logo {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.restaurant-info h4 {
    margin: 0 0 0.25rem 0;
    color: var(--text-color);
}

.restaurant-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.order-items {
    margin-bottom: 1rem;
}

.order-items .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.order-items .item:last-child {
    border-bottom: none;
}

.order-total {
    text-align: right;
    padding: 1rem 0;
    border-top: 2px solid #eee;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.delivery-tracking,
.preparation-time {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-top: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tracking-info,
.preparation-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
}

.track-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background 0.3s ease;
}

.track-btn:hover {
    background: #e55a2b;
}

.order-actions {
    display: flex;
    gap: 1rem;
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    border-top: 1px solid #eee;
}

.reorder-btn,
.rate-btn {
    flex: 1;
    background: white;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.reorder-btn:hover,
.rate-btn:hover {
    background: var(--primary-color);
    color: white;
}

.empty-orders {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
}

.empty-orders i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.browse-restaurants-btn {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    display: inline-block;
    margin-top: 1rem;
    transition: background 0.3s ease;
}

.browse-restaurants-btn:hover {
    background: #e55a2b;
}

/* Tracking Modal Styles */
.tracking-steps {
    margin-bottom: 2rem;
}

.step {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: var(--border-radius);
    background: #f8f9fa;
    position: relative;
}

.step.completed {
    background: #d4edda;
}

.step.active {
    background: #fff3cd;
    border: 2px solid var(--primary-color);
}

.step i {
    font-size: 1.5rem;
    color: #666;
}

.step.completed i {
    color: #28a745;
}

.step.active i {
    color: var(--primary-color);
}

.step-info h4 {
    margin: 0 0 0.25rem 0;
}

.step-info p {
    margin: 0 0 0.25rem 0;
    color: #666;
}

.step-info small {
    color: #999;
}

.map-placeholder {
    background: #f8f9fa;
    border: 2px dashed #ddd;
    border-radius: var(--border-radius);
    padding: 3rem;
    text-align: center;
    color: #666;
}

.map-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #ddd;
}

/* Rating Modal Styles */
.star-rating {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin: 1rem 0;
}

.star-rating i {
    font-size: 2rem;
    color: #ddd;
    cursor: pointer;
    transition: color 0.3s ease;
}

.star-rating i:hover {
    color: #ffc107;
}

.review-section textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    resize: vertical;
    font-family: inherit;
}

.submit-rating-btn {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;
    margin-top: 1rem;
}

.submit-rating-btn:hover {
    background: #e55a2b;
}

/* Mobile Modal Styles */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        max-height: 95vh;
    }

    .about-grid,
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-hero h1,
    .deals-hero h1 {
        font-size: 2rem;
    }

    .filter-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .deals-grid {
        grid-template-columns: 1fr;
    }

    .restaurant-info {
        flex-direction: column;
    }

    .restaurant-info img {
        width: 100%;
        height: 200px;
    }

    .menu-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .item-price {
        text-align: left;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}

/* Cart Button */
.cart-btn {
    position: relative;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.8rem;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    margin-left: 1rem;
}

.cart-btn:hover {
    background: #e65100;
    transform: scale(1.1);
}

.cart-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff1744;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Cart Modal Styles */
.cart-items {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 1rem;
}

.cart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.cart-item:last-child {
    border-bottom: none;
}

.item-details {
    flex: 1;
}

.item-details h4 {
    margin: 0 0 0.5rem 0;
    color: var(--secondary-color);
}

.item-details p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-controls button {
    background: var(--primary-color);
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    font-weight: bold;
}

.quantity-controls span {
    min-width: 30px;
    text-align: center;
    font-weight: bold;
}

.item-total {
    font-weight: bold;
    color: var(--primary-color);
    min-width: 80px;
    text-align: right;
}

.remove-item {
    background: #ff1744;
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.remove-item:hover {
    background: #d50000;
}

.cart-summary {
    border-top: 2px solid #eee;
    padding-top: 1rem;
    text-align: center;
}

.total {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

.checkout-btn, .place-order-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: 600;
    width: 100%;
    transition: background 0.3s ease;
}

.checkout-btn:hover, .place-order-btn:hover {
    background: #e65100;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--secondary-color);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.order-summary {
    background: #f9f9f9;
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1.5rem;
}

.order-summary h3 {
    margin: 0 0 1rem 0;
    color: var(--secondary-color);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.summary-total {
    border-top: 1px solid #ddd;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
    font-size: 1.1rem;
}