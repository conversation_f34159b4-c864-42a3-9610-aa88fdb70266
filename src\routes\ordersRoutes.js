const express = require('express');
const ordersService = require('../services/ordersService');

const router = express.Router();

// Middleware to get user ID from request
const getUser = (req, res, next) => {
    req.userId = req.headers['x-user-id'] || req.body.userId || `user_${Date.now()}`;
    next();
};

router.use(getUser);

// Place new order
router.post('/', async (req, res) => {
    try {
        const { restaurantId, items, totalAmount, deliveryAddress, phoneNumber, notes } = req.body;

        if (!restaurantId || !items || !totalAmount || !deliveryAddress || !phoneNumber) {
            return res.status(400).json({
                error: 'Missing required fields: restaurantId, items, totalAmount, deliveryAddress, phoneNumber'
            });
        }

        const order = await ordersService.placeOrder(
            req.userId,
            restaurantId,
            items,
            totalAmount,
            deliveryAddress,
            phoneNumber,
            notes
        );

        res.status(201).json(order);
    } catch (error) {
        console.error('Error placing order:', error);
        res.status(500).json({ error: error.message });
    }
});

// Get user orders
router.get('/', async (req, res) => {
    try {
        const orders = await ordersService.getUserOrders(req.userId);
        res.json(orders);
    } catch (error) {
        console.error('Error getting orders:', error);
        res.status(500).json({ error: error.message });
    }
});

// Get specific order
router.get('/:orderId', async (req, res) => {
    try {
        const { orderId } = req.params;
        const order = await ordersService.getOrderById(orderId);
        res.json(order);
    } catch (error) {
        console.error('Error getting order:', error);
        res.status(500).json({ error: error.message });
    }
});

// Update order status
router.patch('/:orderId/status', async (req, res) => {
    try {
        const { orderId } = req.params;
        const { status } = req.body;

        if (!status) {
            return res.status(400).json({ error: 'Status is required' });
        }

        await ordersService.updateOrderStatus(orderId, status);
        res.json({ message: 'Order status updated successfully' });
    } catch (error) {
        console.error('Error updating order status:', error);
        res.status(500).json({ error: error.message });
    }
});

// Get all orders (admin)
router.get('/admin/all', async (req, res) => {
    try {
        const orders = await ordersService.getAllOrders();
        res.json(orders);
    } catch (error) {
        console.error('Error getting all orders:', error);
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;
