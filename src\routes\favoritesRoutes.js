const express = require('express');
const { FavoritesService } = require('../services/favoritesService');

const router = express.Router();

// Middleware to get user ID from request
const getUser = (req, res, next) => {
    req.userId = req.headers['x-user-id'] || 'anonymous_user';
    next();
};

router.use(getUser);

// Add favorite
router.post('/', async (req, res) => {
    try {
        const { restaurantId, itemId } = req.body;
        const favoritesService = new FavoritesService(req.userId);
        await favoritesService.addFavorite(restaurantId, itemId);
        res.status(201).json({ message: 'Item added to favorites' });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Remove favorite
router.delete('/:restaurantId/:itemId', async (req, res) => {
    try {
        const { restaurantId, itemId } = req.params;
        const favoritesService = new FavoritesService(req.userId);
        await favoritesService.removeFavorite(restaurantId, itemId);
        res.json({ message: 'Item removed from favorites' });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Get all favorites
router.get('/', async (req, res) => {
    try {
        const favoritesService = new FavoritesService(req.userId);
        const favorites = await favoritesService.getFavorites();
        res.json(favorites);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Check if item is favorite
router.get('/:restaurantId/:itemId', async (req, res) => {
    try {
        const { restaurantId, itemId } = req.params;
        const favoritesService = new FavoritesService(req.userId);
        const isFavorite = await favoritesService.isFavorite(restaurantId, itemId);
        res.json({ isFavorite });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;
