// Orders page functionality
class OrdersManager {
    constructor() {
        this.orders = [];
        this.currentFilter = 'all';
        this.init();
    }

    init() {
        this.loadOrders();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Filter buttons
        const filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                // Update active state
                filterButtons.forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                
                this.currentFilter = e.target.dataset.status;
                this.filterOrders();
            });
        });
    }

    async loadOrders() {
        try {
            // In a real application, this would fetch from an API
            // For now, we'll use the sample orders from the HTML
            this.orders = this.getSampleOrders();
            this.displayOrders(this.orders);
        } catch (error) {
            console.error('Error loading orders:', error);
            this.showError('Failed to load orders');
        }
    }

    getSampleOrders() {
        return [
            {
                id: 'TRB001',
                date: 'December 15, 2024 - 2:30 PM',
                status: 'delivered',
                restaurant: {
                    name: 'Nawab Palace',
                    description: 'Traditional Pakistani Cuisine',
                    logo: '/assets/images/nawab_logo.png'
                },
                items: [
                    { name: 'Chicken Biryani', quantity: 2, price: 360 },
                    { name: 'Chicken Karahi', quantity: 1, price: 450 }
                ],
                total: 1245,
                deliveryTime: null
            },
            {
                id: 'TRB002',
                date: 'December 15, 2024 - 4:15 PM',
                status: 'on_way',
                restaurant: {
                    name: 'Crust Bros',
                    description: 'Italian & Fast Food',
                    logo: '/assets/images/CrustBrosLogo.jpg'
                },
                items: [
                    { name: 'Margherita Pizza (Large)', quantity: 1, price: 850 },
                    { name: 'Garlic Bread', quantity: 1, price: 200 }
                ],
                total: 1125,
                deliveryTime: '15-20 minutes'
            },
            {
                id: 'TRB003',
                date: 'December 15, 2024 - 5:00 PM',
                status: 'preparing',
                restaurant: {
                    name: 'Eatway',
                    description: 'American Fast Food',
                    logo: '/assets/images/eatway_logo.png'
                },
                items: [
                    { name: 'Classic Burger', quantity: 2, price: 300 },
                    { name: 'French Fries', quantity: 2, price: 150 }
                ],
                total: 975,
                preparationTime: '10-15 minutes'
            },
            {
                id: 'TRB004',
                date: 'December 14, 2024 - 7:30 PM',
                status: 'delivered',
                restaurant: {
                    name: 'Meet N Eat',
                    description: 'Chinese & Continental',
                    logo: '/assets/images/meetneat.jpg'
                },
                items: [
                    { name: 'Chicken Noodles', quantity: 1, price: 320 },
                    { name: 'Spring Rolls', quantity: 1, price: 180 }
                ],
                total: 575,
                deliveryTime: null
            },
            {
                id: 'TRB005',
                date: 'December 13, 2024 - 1:15 PM',
                status: 'delivered',
                restaurant: {
                    name: 'MFC',
                    description: 'Fried Chicken & Fast Food',
                    logo: '/assets/images/mfc.jpg'
                },
                items: [
                    { name: 'Chicken Shawarma', quantity: 2, price: 200 },
                    { name: 'Fries', quantity: 1, price: 120 }
                ],
                total: 595,
                deliveryTime: null
            }
        ];
    }

    filterOrders() {
        let filteredOrders;
        
        if (this.currentFilter === 'all') {
            filteredOrders = this.orders;
        } else {
            filteredOrders = this.orders.filter(order => order.status === this.currentFilter);
        }
        
        this.displayOrders(filteredOrders);
    }

    displayOrders(orders) {
        const ordersList = document.getElementById('ordersList');
        const emptyState = document.querySelector('.empty-orders');
        
        if (orders.length === 0) {
            ordersList.style.display = 'none';
            emptyState.style.display = 'block';
            return;
        }
        
        ordersList.style.display = 'block';
        emptyState.style.display = 'none';
        
        ordersList.innerHTML = orders.map(order => this.createOrderCard(order)).join('');
    }

    createOrderCard(order) {
        const statusConfig = this.getStatusConfig(order.status);
        
        return `
            <div class="order-card" data-status="${order.status}">
                <div class="order-header">
                    <div class="order-info">
                        <h3>Order #${order.id}</h3>
                        <p class="order-date">${order.date}</p>
                    </div>
                    <div class="order-status ${order.status}">
                        <i class="${statusConfig.icon}"></i>
                        <span>${statusConfig.text}</span>
                    </div>
                </div>
                <div class="order-details">
                    <div class="restaurant-info">
                        <img src="${order.restaurant.logo}" alt="${order.restaurant.name}" class="restaurant-logo">
                        <div>
                            <h4>${order.restaurant.name}</h4>
                            <p>${order.restaurant.description}</p>
                        </div>
                    </div>
                    <div class="order-items">
                        ${order.items.map(item => `
                            <div class="item">
                                <span>${item.name} x${item.quantity}</span>
                                <span>Rs. ${item.price * item.quantity}</span>
                            </div>
                        `).join('')}
                    </div>
                    <div class="order-total">
                        <strong>Total: Rs. ${order.total} (including delivery)</strong>
                    </div>
                    ${this.getOrderStatusInfo(order)}
                </div>
                ${this.getOrderActions(order)}
            </div>
        `;
    }

    getStatusConfig(status) {
        const configs = {
            pending: { icon: 'fas fa-clock', text: 'Pending' },
            preparing: { icon: 'fas fa-utensils', text: 'Preparing' },
            on_way: { icon: 'fas fa-motorcycle', text: 'On the Way' },
            delivered: { icon: 'fas fa-check-circle', text: 'Delivered' },
            cancelled: { icon: 'fas fa-times-circle', text: 'Cancelled' }
        };
        return configs[status] || configs.pending;
    }

    getOrderStatusInfo(order) {
        switch (order.status) {
            case 'on_way':
                return `
                    <div class="delivery-tracking">
                        <div class="tracking-info">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Estimated delivery: ${order.deliveryTime}</span>
                        </div>
                        <button class="track-btn" onclick="ordersManager.trackOrder('${order.id}')">
                            <i class="fas fa-route"></i> Track Order
                        </button>
                    </div>
                `;
            case 'preparing':
                return `
                    <div class="preparation-time">
                        <i class="fas fa-clock"></i>
                        <span>Estimated preparation time: ${order.preparationTime}</span>
                    </div>
                `;
            default:
                return '';
        }
    }

    getOrderActions(order) {
        if (order.status === 'delivered') {
            return `
                <div class="order-actions">
                    <button class="reorder-btn" onclick="ordersManager.reorderItems('${order.id}')">
                        <i class="fas fa-redo"></i> Reorder
                    </button>
                    <button class="rate-btn" onclick="ordersManager.rateOrder('${order.id}')">
                        <i class="fas fa-star"></i> Rate Order
                    </button>
                </div>
            `;
        }
        return '';
    }

    trackOrder(orderId) {
        // Show tracking modal
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Track Order #${orderId}</h2>
                    <button class="close-modal" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="tracking-steps">
                        <div class="step completed">
                            <i class="fas fa-check-circle"></i>
                            <div class="step-info">
                                <h4>Order Confirmed</h4>
                                <p>Your order has been confirmed</p>
                                <small>4:15 PM</small>
                            </div>
                        </div>
                        <div class="step completed">
                            <i class="fas fa-utensils"></i>
                            <div class="step-info">
                                <h4>Preparing</h4>
                                <p>Restaurant is preparing your order</p>
                                <small>4:20 PM</small>
                            </div>
                        </div>
                        <div class="step active">
                            <i class="fas fa-motorcycle"></i>
                            <div class="step-info">
                                <h4>On the Way</h4>
                                <p>Your order is on the way</p>
                                <small>4:35 PM</small>
                            </div>
                        </div>
                        <div class="step">
                            <i class="fas fa-home"></i>
                            <div class="step-info">
                                <h4>Delivered</h4>
                                <p>Order will be delivered soon</p>
                                <small>ETA: 4:50 PM</small>
                            </div>
                        </div>
                    </div>
                    <div class="delivery-map">
                        <div class="map-placeholder">
                            <i class="fas fa-map"></i>
                            <p>Live tracking map would appear here</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    reorderItems(orderId) {
        const order = this.orders.find(o => o.id === orderId);
        if (order) {
            // Add items to cart
            order.items.forEach(item => {
                // This would typically add to the cart system
                console.log(`Adding ${item.name} x${item.quantity} to cart`);
            });
            
            this.showNotification(`Items from order #${orderId} added to cart!`);
            
            // Redirect to cart or restaurant page
            setTimeout(() => {
                window.location.href = 'cart.html';
            }, 1500);
        }
    }

    rateOrder(orderId) {
        // Show rating modal
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Rate Your Order</h2>
                    <button class="close-modal" onclick="this.closest('.modal-overlay').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="rating-section">
                        <h3>How was your food?</h3>
                        <div class="star-rating" data-rating="0">
                            <i class="fas fa-star" data-star="1"></i>
                            <i class="fas fa-star" data-star="2"></i>
                            <i class="fas fa-star" data-star="3"></i>
                            <i class="fas fa-star" data-star="4"></i>
                            <i class="fas fa-star" data-star="5"></i>
                        </div>
                    </div>
                    <div class="review-section">
                        <h3>Leave a review (optional)</h3>
                        <textarea placeholder="Tell us about your experience..." rows="4"></textarea>
                    </div>
                    <button class="submit-rating-btn" onclick="ordersManager.submitRating('${orderId}')">
                        Submit Rating
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Add star rating functionality
        const stars = modal.querySelectorAll('.star-rating i');
        const ratingContainer = modal.querySelector('.star-rating');
        
        stars.forEach(star => {
            star.addEventListener('click', () => {
                const rating = parseInt(star.dataset.star);
                ratingContainer.dataset.rating = rating;
                
                stars.forEach((s, index) => {
                    if (index < rating) {
                        s.style.color = '#ffc107';
                    } else {
                        s.style.color = '#ddd';
                    }
                });
            });
        });
    }

    submitRating(orderId) {
        this.showNotification('Thank you for your feedback!');
        document.querySelector('.modal-overlay').remove();
    }

    showError(message) {
        const ordersList = document.getElementById('ordersList');
        ordersList.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-circle"></i>
                <h3>Error Loading Orders</h3>
                <p>${message}</p>
                <button onclick="ordersManager.loadOrders()" class="retry-btn">Try Again</button>
            </div>
        `;
    }

    showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => notification.classList.add('show'), 100);
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}

// Initialize orders manager
let ordersManager;
document.addEventListener('DOMContentLoaded', () => {
    ordersManager = new OrdersManager();
});
