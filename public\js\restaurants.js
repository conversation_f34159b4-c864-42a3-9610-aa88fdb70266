// Restaurant data and functionality
const restaurantData = {
    'Pizza Point': {
        name: 'Pizza Point',
        image: '/assets/images/CrustBros.jpg',
        rating: 4.5,
        menu: [
            { name: 'Pepperoni Pizza', price: 1200, image: '/assets/images/pizza1.jpg' },
            { name: 'Chicken Tikka Pizza', price: 1400, image: '/assets/images/pizza2.jpg' },
            { name: 'BBQ Pizza', price: 1300, image: '/assets/images/pizza3.jpg' },
            { name: 'Fajita Pizza', price: 1500, image: '/assets/images/pizza4.jpg' }
        ]
    },
    'Burger Lab': {
        name: 'Burger Lab',
        image: '/assets/images/burger.jpg',
        rating: 4.3,
        menu: [
            { name: 'Classic Burger', price: 600, image: '/assets/images/burger1.jpg' },
            { name: 'Chicken Burger', price: 700, image: '/assets/images/burger2.jpg' },
            { name: 'BBQ Burger', price: 800, image: '/assets/images/burger3.jpg' },
            { name: 'Double Patty Burger', price: 900, image: '/assets/images/burger4.jpg' }
        ]
    },
    'Chinese Wok': {
        name: 'Chinese Wok',
        image: '/assets/images/chinese_wok.jpg',
        rating: 4.2,
        menu: [
            { name: 'Chicken Chowmein', price: 500, image: '/assets/images/chinese1.jpg' },
            { name: 'Fried Rice', price: 400, image: '/assets/images/chinese2.jpg' },
            { name: 'Manchurian', price: 600, image: '/assets/images/chinese3.jpg' },
            { name: 'Sweet & Sour Soup', price: 300, image: '/assets/images/chinese4.jpg' }
        ]
    },
    'Desi Dhaba': {
        name: 'Desi Dhaba',
        image: '/assets/images/desi_dhaba.jpg',
        rating: 4.4,
        menu: [
            { name: 'Chicken Karahi', price: 1200, image: '/assets/images/desi1.jpg' },
            { name: 'Biryani', price: 300, image: '/assets/images/biryani_icon.png' },
            { name: 'Naan', price: 40, image: '/assets/images/desi3.jpg' },
            { name: 'Chicken Tikka', price: 800, image: '/assets/images/desi4.jpg' }
        ]
    }
};

// Load and display restaurants
function loadRestaurants() {
    const restaurantsGrid = document.querySelector('.restaurants-grid');
    if (!restaurantsGrid) return;

    restaurantsGrid.innerHTML = Object.values(restaurantData)
        .map(restaurant => createRestaurantCard(restaurant))
        .join('');
}

// Create restaurant card
function createRestaurantCard(restaurant) {
    return `
        <div class="restaurant-card">
            <div class="restaurant-image">
                <img src="${restaurant.image}" alt="${restaurant.name}" onerror="this.src='/assets/images/placeholder.jpg'">
            </div>
            <div class="restaurant-content">
                <h3>${restaurant.name}</h3>
                <div class="restaurant-rating">
                    <i class="fas fa-star"></i>
                    <span>${restaurant.rating}</span>
                </div>
                <button class="view-menu-btn" onclick="viewRestaurantMenu('${restaurant.name}')">
                    View Menu
                </button>
            </div>
        </div>
    `;
}

// View restaurant menu
function viewRestaurantMenu(restaurantName) {
    const restaurant = restaurantData[restaurantName];
    if (!restaurant) return;

    const menuContainer = document.querySelector('.menu-container');
    if (!menuContainer) {
        window.location.href = `/restaurants.html?restaurant=${encodeURIComponent(restaurantName)}`;
        return;
    }

    const menuHtml = restaurant.menu.map(item => `
        <div class="menu-item">
            <div class="item-image">
                <img src="${item.image}" alt="${item.name}" onerror="this.src='/assets/images/placeholder.jpg'">
            </div>
            <div class="item-content">
                <h4>${item.name}</h4>
                <p class="price">Rs. ${item.price}</p>
                <button onclick="addToCart('${restaurantName}', '${item.name}', ${item.price})">
                    Add to Cart
                </button>
            </div>
        </div>
    `).join('');

    menuContainer.innerHTML = menuHtml;
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    loadRestaurants();

    // Check for restaurant parameter in URL
    const params = new URLSearchParams(window.location.search);
    const restaurant = params.get('restaurant');
    if (restaurant) {
        viewRestaurantMenu(restaurant);
    }
});
