<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Find and order from the best restaurants in Jahanian">
    <meta name="theme-color" content="#2196F3">
    <title>Torbaaz Food Delivery</title>
    
    <!-- PWA Support -->
    <link rel="manifest" href="/manifest.json">
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/favicon.png">
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/chat.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header>
        <nav class="flex items-center justify-between max-w-7xl mx-auto px-4 py-2">
            <div class="logo flex items-center gap-3">
                <a href="/" tabindex="0" aria-label="Go to homepage" class="outline-none focus:ring-2 focus:ring-orange-500 rounded">
                    <img src="/assets/images/logo.png" alt="Torbaaz Logo" class="h-12 w-auto object-contain" />
                </a>
                <h1 class="text-2xl font-bold text-orange-500">Torbaaz</h1>
            </div>
            <div class="search-bar">
                <input type="text" placeholder="Search for restaurants or dishes...">
                <button><i class="fas fa-search"></i></button>
            </div>
            <div class="nav-links">
                <a href="/index.html" class="active">Home</a>
                <a href="/restaurants.html">Restaurants</a>
                <a href="/deals.html">Deals</a>
                <a href="/orders.html">Orders</a>
                <button class="cart-btn" onclick="viewCart()">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-badge" style="display: none;">0</span>
                </button>
            </div>
        </nav>
    </header>

    <!-- Enhanced Hero Section -->
    <section class="hero-modern">
        <div class="hero-background">
            <div class="hero-overlay"></div>
            <img src="/assets/images/hero-food.jpg" alt="Delicious Food" class="hero-image">
        </div>
        <div class="hero-content-modern">
            <div class="hero-text">
                <h1 class="hero-title">
                    <span class="hero-title-main">Delicious Food</span>
                    <span class="hero-title-sub">Delivered to Your Doorstep</span>
                </h1>
                <p class="hero-description">
                    Discover the best food from local restaurants in Jahanian.
                    Fast delivery, fresh ingredients, and amazing flavors await you.
                </p>
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number" id="restaurantCount">15+</span>
                        <span class="stat-label">Restaurants</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">500+</span>
                        <span class="stat-label">Menu Items</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">20min</span>
                        <span class="stat-label">Avg Delivery</span>
                    </div>
                </div>
                <div class="hero-actions">
                    <button class="cta-primary" onclick="scrollToRestaurants()">
                        <i class="fas fa-utensils"></i>
                        Order Now
                    </button>
                    <button class="cta-secondary" onclick="openChatWidget()">
                        <i class="fas fa-robot"></i>
                        Ask Jarvis
                    </button>
                </div>
            </div>
        </div>

        <!-- Dynamic Category Chips -->
        <div class="category-section">
            <div class="container">
                <h3 class="category-title">What are you craving?</h3>
                <div class="category-chips-modern" id="categoryChips">
                    <!-- Categories will be dynamically loaded -->
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Items Section -->
    <section class="featured-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-star"></i>
                    Featured Items
                </h2>
                <p class="section-subtitle">Hand-picked favorites from our top restaurants</p>
            </div>
            <div class="featured-grid" id="featuredGrid">
                <!-- Featured items will be dynamically loaded -->
            </div>
        </div>
    </section>

    <!-- Deals Section Enhanced -->
    <section class="deals-section-modern">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-fire"></i>
                    Today's Hot Deals
                </h2>
                <p class="section-subtitle">Limited time offers you can't miss</p>
                <div class="deals-timer">
                    <i class="fas fa-clock"></i>
                    <span>Deals refresh in: <span id="dealsTimer">23:45:12</span></span>
                </div>
            </div>
            <div class="deals-container-modern" id="dealsContainer">
                <!-- Deals will be dynamically loaded -->
            </div>
        </div>
    </section>

    <!-- Restaurants Section Enhanced -->
    <section class="restaurants-section-modern" id="restaurantsSection">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-store"></i>
                    Popular Restaurants
                </h2>
                <p class="section-subtitle">Discover amazing restaurants near you</p>
                <div class="filter-controls">
                    <div class="filter-group">
                        <button class="filter-btn active" data-filter="all">All</button>
                        <button class="filter-btn" data-filter="rating">Top Rated</button>
                        <button class="filter-btn" data-filter="delivery">Fast Delivery</button>
                        <button class="filter-btn" data-filter="offers">Special Offers</button>
                    </div>
                    <div class="view-toggle">
                        <button class="view-btn active" data-view="grid">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="view-btn" data-view="list">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="restaurants-grid-modern" id="restaurantsGrid">
                <!-- Restaurants will be dynamically loaded -->
            </div>
            <div class="load-more-section">
                <button class="load-more-btn" id="loadMoreBtn">
                    <i class="fas fa-plus"></i>
                    Load More Restaurants
                </button>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="newsletter-section">
        <div class="container">
            <div class="newsletter-content">
                <div class="newsletter-text">
                    <h3>Stay Updated with Torbaaz</h3>
                    <p>Get notified about new restaurants, exclusive deals, and special offers</p>
                </div>
                <div class="newsletter-form">
                    <input type="email" placeholder="Enter your email address" id="newsletterEmail">
                    <button type="button" onclick="subscribeNewsletter()">
                        <i class="fas fa-paper-plane"></i>
                        Subscribe
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <h3>About Us</h3>
                <ul>
                    <li><a href="#">Our Story</a></li>
                    <li><a href="#">Blog</a></li>
                    <li><a href="#">Careers</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>For Restaurants</h3>
                <ul>
                    <li><a href="#">Partner with us</a></li>
                    <li><a href="#">Restaurant app</a></li>
                    <li><a href="#">Business Blog</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Support</h3>
                <ul>
                    <li><a href="#">Help Center</a></li>
                    <li><a href="#">Contact Us</a></li>
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Follow Us</h3>
                <ul>
                    <li><a href="#"><i class="fab fa-facebook"></i> Facebook</a></li>
                    <li><a href="#"><i class="fab fa-instagram"></i> Instagram</a></li>
                    <li><a href="#"><i class="fab fa-twitter"></i> Twitter</a></li>
                </ul>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 Torbaaz Food Delivery. All rights reserved.</p>
        </div>
    </footer>

    <!-- Enhanced Chat Widget -->
    <div class="chat-widget chat-collapsed" id="chatWidget" tabindex="0" role="complementary" aria-label="Chat with Jarvis">
        <div class="chat-toggle" role="button" tabindex="0" aria-label="Toggle chat expansion" title="Drag to move">
            <div class="chat-title">
                <i class="fas fa-grip-vertical handle-icon" aria-hidden="true"></i>
                <i class="fas fa-robot" aria-hidden="true"></i>
                <span>Ask Jarvis</span>
            </div>
            <div class="chat-controls">
                <button class="expand-btn top" title="Expand chat" aria-label="Expand chat">
                    <i class="fas fa-expand" aria-hidden="true"></i>
                </button>
                <button class="chat-control-btn maximize-btn" title="Toggle fullscreen" aria-label="Toggle fullscreen">
                    <i class="fas fa-expand-arrows-alt" aria-hidden="true"></i>
                </button>
                <button class="chat-control-btn minimize-btn" title="Minimize" aria-label="Minimize chat">
                    <i class="fas fa-minus" aria-hidden="true"></i>
                </button>
            </div>
        </div>
        <div class="chat-content">
            <div class="chat-container">
                <div class="chat-messages" role="log" aria-live="polite">
                    <!-- Messages will be dynamically added here -->
                </div>
            </div>
            <div class="chat-input">
                <textarea 
                    class="message-input" 
                    placeholder="Ask me anything..." 
                    rows="1" 
                    aria-label="Type your message"
                    role="textbox"
                ></textarea>
                <button class="send-button" title="Send message" aria-label="Send message">
                    <i class="fas fa-paper-plane" aria-hidden="true"></i>
                </button>
                <button class="expand-btn bottom" title="Expand chat" aria-label="Expand chat">
                    <i class="fas fa-expand" aria-hidden="true"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/main.js"></script>
    <script src="/js/chat.js"></script>
    <script src="/js/search.js"></script>
    <script src="/js/app.js"></script>
</body>
</html>