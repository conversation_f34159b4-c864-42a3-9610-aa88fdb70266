const dataset = require('../data/dataset');

/**
 * Get food details by food name
 * @param {string} foodName
 * @returns {object|null}
 */
const getFoodDetails = async (foodName) => {
  for (const restaurant of dataset.restaurants) {
    const item = restaurant.menu.find(m => m.name.toLowerCase() === foodName.toLowerCase());
    if (item) return item;
  }
  return null;
};

module.exports = getFoodDetails;
