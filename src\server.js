require('dotenv').config();
const express = require('express');
const path = require('path');
const chatRoutes = require('./routes/chatRoutes');
const favoritesRoutes = require('./routes/favoritesRoutes');
const ordersRoutes = require('./routes/ordersRoutes');
const restaurantRoutes = require('./routes/restaurantRoutes');
const pageRoutes = require('./routes/pageRoutes');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.static(path.join(__dirname, '../public')));
app.use('/assets', express.static(path.join(__dirname, '../assets')));

// Validate required environment variables
if (!process.env.OPENAI_API_KEY || !process.env.OPENAI_ASSISTANT_ID || !process.env.SUPABASE_URL || !process.env.SUPABASE_KEY) {
    console.error('Error: Required environment variables are missing.');
    console.error('Please ensure OPENAI_API_KEY, OPENAI_ASSISTANT_ID, SUPABASE_URL, and SUPABASE_KEY are set in your .env file.');
    process.exit(1);
}

// Page Routes
app.use('/', pageRoutes);

// API Routes
app.use('/api/chat', chatRoutes);
app.use('/api/favorites', favoritesRoutes);
app.use('/api/orders', ordersRoutes);
app.use('/api/restaurants', restaurantRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({
        error: 'Something went wrong!',
        details: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'healthy' });
});

// Start server
app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
    console.log(`OpenAI Assistant ID: ${process.env.OPENAI_ASSISTANT_ID}`);
}); 