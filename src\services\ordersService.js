const supabase = require('../config/supabase');

class OrdersService {
    async placeOrder(userId, restaurantId, items, totalAmount, deliveryAddress, phoneNumber, notes = '') {
        try {
            // Create the order
            const { data: order, error: orderError } = await supabase
                .from('orders')
                .insert({
                    user_id: userId,
                    restaurant_id: restaurantId,
                    total_amount: totalAmount,
                    status: 'pending',
                    delivery_address: deliveryAddress,
                    phone_number: phoneNumber,
                    notes: notes
                })
                .select()
                .single();

            if (orderError) throw orderError;

            // Create order items
            const orderItems = items.map(item => ({
                order_id: order.id,
                menu_item_id: item.menu_item_id,
                quantity: item.quantity,
                price: item.price
            }));

            const { error: itemsError } = await supabase
                .from('order_items')
                .insert(orderItems);

            if (itemsError) throw itemsError;

            return order;
        } catch (error) {
            console.error('Error placing order:', error);
            throw error;
        }
    }

    async getUserOrders(userId) {
        try {
            const { data, error } = await supabase
                .from('orders')
                .select(`
                    *,
                    restaurants(name, image_url),
                    order_items(
                        *,
                        menu_items(name, price)
                    )
                `)
                .eq('user_id', userId)
                .order('created_at', { ascending: false });

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error getting orders:', error);
            throw error;
        }
    }

    async updateOrderStatus(orderId, status) {
        try {
            const { error } = await supabase
                .from('orders')
                .update({ status })
                .eq('id', orderId);

            if (error) throw error;
            return true;
        } catch (error) {
            console.error('Error updating order status:', error);
            throw error;
        }
    }

    async getOrderById(orderId) {
        try {
            const { data, error } = await supabase
                .from('orders')
                .select(`
                    *,
                    restaurants(name, image_url, delivery_time),
                    order_items(
                        *,
                        menu_items(name, price, description)
                    )
                `)
                .eq('id', orderId)
                .single();

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error getting order:', error);
            throw error;
        }
    }

    async getAllOrders() {
        try {
            const { data, error } = await supabase
                .from('orders')
                .select(`
                    *,
                    restaurants(name),
                    order_items(quantity, menu_items(name))
                `)
                .order('created_at', { ascending: false });

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error getting all orders:', error);
            throw error;
        }
    }
}

module.exports = new OrdersService();
