const { supabase } = require('../config/supabase');

class FavoritesService {
    constructor(userId) {
        this.userId = userId;
    }

    async addFavorite(restaurantId, itemId) {
        try {
            const { error } = await supabase
                .from('favorites')
                .insert({
                    user_id: this.userId,
                    restaurant_id: restaurantId,
                    item_id: itemId
                });

            if (error) throw error;
            return true;
        } catch (error) {
            console.error('Error adding favorite:', error);
            throw error;
        }
    }

    async removeFavorite(restaurantId, itemId) {
        try {
            const { error } = await supabase
                .from('favorites')
                .delete()
                .eq('user_id', this.userId)
                .eq('restaurant_id', restaurantId)
                .eq('item_id', itemId);

            if (error) throw error;
            return true;
        } catch (error) {
            console.error('Error removing favorite:', error);
            throw error;
        }
    }

    async getFavorites() {
        try {
            const { data, error } = await supabase
                .from('favorites')
                .select('*')
                .eq('user_id', this.userId);

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error getting favorites:', error);
            throw error;
        }
    }

    async isFavorite(restaurantId, itemId) {
        try {
            const { count, error } = await supabase
                .from('favorites')
                .select('*', { count: 'exact', head: true })
                .eq('user_id', this.userId)
                .eq('restaurant_id', restaurantId)
                .eq('item_id', itemId);

            if (error) throw error;
            return count > 0;
        } catch (error) {
            console.error('Error checking favorite:', error);
            throw error;
        }
    }
}

module.exports = { FavoritesService };
